"""
DataAggregator 和 StatisticsCalculator 類別的單元測試

測試資料彙總正確性、統計計算準確性和時間範圍追蹤功能。
"""

import unittest
from datetime import datetime
from analyze_logs import DataAggregator, StatisticsCalculator, HTTPRequestInfo, EndpointStats


class TestDataAggregator(unittest.TestCase):
    """測試 DataAggregator 類別"""

    def setUp(self):
        """設定測試環境"""
        self.aggregator = DataAggregator()

    def test_add_single_request(self):
        """測試添加單一請求"""
        # 建立測試資料
        request_info = HTTPRequestInfo(
            method="GET",
            path="/api/users",
            status_code=200,
            elapsed_time=150.5
        )
        timestamp = datetime(2023, 1, 1, 12, 0, 0)

        # 添加請求
        self.aggregator.add_request(request_info, timestamp)

        # 驗證結果
        aggregated_data = self.aggregator.get_aggregated_data()
        self.assertEqual(len(aggregated_data), 1)

        endpoint_key = ("GET", "/api/users")
        self.assertIn(endpoint_key, aggregated_data)

        stats = aggregated_data[endpoint_key]
        self.assertEqual(stats.count, 1)
        self.assertEqual(stats.status_codes[200], 1)
        self.assertEqual(stats.elapsed_times, [150.5])

    def test_add_multiple_requests_same_endpoint(self):
        """測試添加多個相同 endpoint 的請求"""
        # 建立測試資料
        requests = [
            HTTPRequestInfo("GET", "/api/users", 200, 100.0),
            HTTPRequestInfo("GET", "/api/users", 200, 200.0),
            HTTPRequestInfo("GET", "/api/users", 404, 50.0),
        ]

        # 添加請求
        for request_info in requests:
            self.aggregator.add_request(request_info)

        # 驗證結果
        aggregated_data = self.aggregator.get_aggregated_data()
        endpoint_key = ("GET", "/api/users")
        stats = aggregated_data[endpoint_key]

        self.assertEqual(stats.count, 3)
        self.assertEqual(stats.status_codes[200], 2)
        self.assertEqual(stats.status_codes[404], 1)
        self.assertEqual(stats.elapsed_times, [100.0, 200.0, 50.0])

    def test_add_multiple_requests_different_endpoints(self):
        """測試添加不同 endpoint 的請求"""
        # 建立測試資料
        requests = [
            HTTPRequestInfo("GET", "/api/users", 200, 100.0),
            HTTPRequestInfo("POST", "/api/users", 201, 250.0),
            HTTPRequestInfo("GET", "/api/orders", 200, 150.0),
        ]

        # 添加請求
        for request_info in requests:
            self.aggregator.add_request(request_info)

        # 驗證結果
        aggregated_data = self.aggregator.get_aggregated_data()
        self.assertEqual(len(aggregated_data), 3)

        # 檢查每個 endpoint
        get_users_stats = aggregated_data[("GET", "/api/users")]
        self.assertEqual(get_users_stats.count, 1)
        self.assertEqual(get_users_stats.status_codes[200], 1)

        post_users_stats = aggregated_data[("POST", "/api/users")]
        self.assertEqual(post_users_stats.count, 1)
        self.assertEqual(post_users_stats.status_codes[201], 1)

        get_orders_stats = aggregated_data[("GET", "/api/orders")]
        self.assertEqual(get_orders_stats.count, 1)
        self.assertEqual(get_orders_stats.status_codes[200], 1)

    def test_time_range_tracking(self):
        """測試時間範圍追蹤"""
        # 建立測試資料
        timestamps = [
            datetime(2023, 1, 1, 10, 0, 0),
            datetime(2023, 1, 1, 12, 0, 0),
            datetime(2023, 1, 1, 8, 0, 0),   # 最早時間
            datetime(2023, 1, 1, 15, 0, 0),  # 最晚時間
        ]

        request_info = HTTPRequestInfo("GET", "/api/test", 200, 100.0)

        # 添加請求
        for timestamp in timestamps:
            self.aggregator.add_request(request_info, timestamp)

        # 驗證時間範圍
        start_time, end_time = self.aggregator.get_time_range()
        self.assertEqual(start_time, datetime(2023, 1, 1, 8, 0, 0))
        self.assertEqual(end_time, datetime(2023, 1, 1, 15, 0, 0))

    def test_time_range_with_none_timestamp(self):
        """測試沒有時間戳記的情況"""
        request_info = HTTPRequestInfo("GET", "/api/test", 200, 100.0)
        self.aggregator.add_request(request_info)

        start_time, end_time = self.aggregator.get_time_range()
        self.assertIsNone(start_time)
        self.assertIsNone(end_time)

    def test_add_invalid_request(self):
        """測試添加無效請求"""
        # 測試 None 請求
        self.aggregator.add_request(None)
        aggregated_data = self.aggregator.get_aggregated_data()
        self.assertEqual(len(aggregated_data), 0)

    def test_empty_aggregator(self):
        """測試空的彙總器"""
        aggregated_data = self.aggregator.get_aggregated_data()
        self.assertEqual(len(aggregated_data), 0)

        start_time, end_time = self.aggregator.get_time_range()
        self.assertIsNone(start_time)
        self.assertIsNone(end_time)


class TestStatisticsCalculator(unittest.TestCase):
    """測試 StatisticsCalculator 類別"""

    def test_calculate_average_elapsed_normal_case(self):
        """測試正常情況下的平均回應時間計算"""
        elapsed_times = [100.0, 200.0, 300.0]
        average = StatisticsCalculator.calculate_average_elapsed(elapsed_times)
        self.assertEqual(average, 200.0)

    def test_calculate_average_elapsed_single_value(self):
        """測試單一數值的平均回應時間計算"""
        elapsed_times = [150.5]
        average = StatisticsCalculator.calculate_average_elapsed(elapsed_times)
        self.assertEqual(average, 150.5)

    def test_calculate_average_elapsed_empty_list(self):
        """測試空列表的平均回應時間計算"""
        elapsed_times = []
        average = StatisticsCalculator.calculate_average_elapsed(elapsed_times)
        self.assertEqual(average, 0.0)

    def test_calculate_average_elapsed_with_decimals(self):
        """測試包含小數的平均回應時間計算"""
        elapsed_times = [100.5, 200.3, 150.7]
        expected_average = (100.5 + 200.3 + 150.7) / 3
        average = StatisticsCalculator.calculate_average_elapsed(elapsed_times)
        self.assertAlmostEqual(average, expected_average, places=2)

    def test_format_status_code_distribution_normal_case(self):
        """測試正常情況下的狀態碼分佈格式化"""
        status_codes = {200: 10, 404: 2, 500: 1}
        formatted = StatisticsCalculator.format_status_code_distribution(status_codes)
        self.assertEqual(formatted, "200: 10, 404: 2, 500: 1")

    def test_format_status_code_distribution_single_code(self):
        """測試單一狀態碼的分佈格式化"""
        status_codes = {200: 5}
        formatted = StatisticsCalculator.format_status_code_distribution(status_codes)
        self.assertEqual(formatted, "200: 5")

    def test_format_status_code_distribution_empty_dict(self):
        """測試空字典的狀態碼分佈格式化"""
        status_codes = {}
        formatted = StatisticsCalculator.format_status_code_distribution(status_codes)
        self.assertEqual(formatted, "無資料")

    def test_format_status_code_distribution_sorted_order(self):
        """測試狀態碼分佈的排序"""
        # 故意使用無序的字典
        status_codes = {500: 1, 200: 10, 404: 2}
        formatted = StatisticsCalculator.format_status_code_distribution(status_codes)
        # 應該按狀態碼排序
        self.assertEqual(formatted, "200: 10, 404: 2, 500: 1")


class TestEndpointStats(unittest.TestCase):
    """測試 EndpointStats 類別"""

    def test_calculate_average_elapsed_with_data(self):
        """測試有資料時的平均回應時間計算"""
        stats = EndpointStats()
        stats.elapsed_times = [100.0, 200.0, 300.0]
        stats.calculate_average_elapsed()
        self.assertEqual(stats.average_elapsed, 200.0)

    def test_calculate_average_elapsed_empty_data(self):
        """測試無資料時的平均回應時間計算"""
        stats = EndpointStats()
        stats.elapsed_times = []
        stats.calculate_average_elapsed()
        self.assertEqual(stats.average_elapsed, 0.0)

    def test_default_values(self):
        """測試預設值"""
        stats = EndpointStats()
        self.assertEqual(stats.count, 0)
        self.assertEqual(stats.status_codes, {})
        self.assertEqual(stats.elapsed_times, [])
        self.assertEqual(stats.average_elapsed, 0.0)


if __name__ == '__main__':
    unittest.main()
