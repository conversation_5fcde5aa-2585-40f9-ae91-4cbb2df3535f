
"""
JKoPay API 日誌分析工具

這個工具專門用於分析 JKoPay API 的線上支付日誌檔案，
解析 HTTP 請求資訊並提供統計分析結果。
"""

import argparse
import json
import os
import re
from collections import defaultdict, Counter
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Iterator, Any


@dataclass
class HTTPRequestInfo:
    """HTTP 請求資訊的資料類別"""
    method: str
    path: str
    status_code: int
    elapsed_time: float


@dataclass
class EndpointStats:
    """API endpoint 統計資料的資料類別"""
    count: int = 0
    status_codes: Dict[int, int] = field(default_factory=dict)
    elapsed_times: List[float] = field(default_factory=list)
    average_elapsed: float = 0.0

    def calculate_average_elapsed(self) -> None:
        """計算並更新平均回應時間"""
        if self.elapsed_times:
            self.average_elapsed = sum(self.elapsed_times) / len(self.elapsed_times)
        else:
            self.average_elapsed = 0.0


# 預編譯正規表示式以提高效能
# 匹配格式: HTTP "RequestMethod" "RequestPath" responded StatusCode in Elapsed ms
# 注意：RenderedMessage 中的方法和路徑都用雙引號包圍
HTTP_PATTERN = re.compile(r'HTTP "(\w+)" "([^"]+)" responded (\d{3}) in ([\d\.]+) ms')


class TimeRangeFilter:
    """時間範圍過濾器，用於過濾指定時間範圍內的請求"""

    def __init__(self, start_time: Optional[datetime] = None, end_time: Optional[datetime] = None):
        """
        初始化時間範圍過濾器

        Args:
            start_time: 開始時間（包含），如果為 None 則不過濾開始時間
            end_time: 結束時間（包含），如果為 None 則不過濾結束時間
        """
        self.start_time = start_time
        self.end_time = end_time

    def is_in_range(self, timestamp: Optional[datetime]) -> bool:
        """
        檢查時間戳記是否在指定範圍內

        Args:
            timestamp: 要檢查的時間戳記

        Returns:
            如果時間戳記在範圍內或沒有時間限制則返回 True，否則返回 False
        """
        if timestamp is None:
            # 如果沒有時間戳記，根據過濾策略決定是否包含
            # 這裡選擇包含沒有時間戳記的記錄
            return True

        # 檢查開始時間
        if self.start_time is not None and timestamp < self.start_time:
            return False

        # 檢查結束時間
        if self.end_time is not None and timestamp > self.end_time:
            return False

        return True

    def has_filter(self) -> bool:
        """
        檢查是否有設定時間過濾條件

        Returns:
            如果有設定開始時間或結束時間則返回 True
        """
        return self.start_time is not None or self.end_time is not None

    def get_description(self) -> str:
        """
        取得時間範圍的描述文字

        Returns:
            時間範圍的描述字串
        """
        if not self.has_filter():
            return "無時間過濾條件"

        parts = []
        if self.start_time:
            parts.append(f"從 {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if self.end_time:
            parts.append(f"至 {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")

        return " ".join(parts)


class JSONLogParser:
    """JSON 日誌解析器，負責解析 JSON 格式的日誌行並提取相關欄位"""

    def __init__(self, error_handler: Optional['ErrorHandler'] = None):
        """
        初始化 JSONLogParser

        Args:
            error_handler: 錯誤處理器實例（可選）
        """
        self.error_handler = error_handler

    def parse_log_line(self, line: str, line_number: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        解析 JSON 字串為字典物件

        Args:
            line: 原始日誌行字串
            line_number: 行號（用於錯誤報告）

        Returns:
            解析後的字典物件，如果解析失敗則返回 None
        """
        try:
            # 移除行尾的換行符號並解析 JSON
            cleaned_line = line.strip()
            if not cleaned_line:
                if self.error_handler:
                    self.error_handler.log_error("empty_line", "空行", line_number)
                return None

            log_data = json.loads(cleaned_line)
            return log_data

        except json.JSONDecodeError as e:
            # JSON 解析錯誤，記錄錯誤並跳過該行
            if self.error_handler:
                self.error_handler.log_error("json_parse_error", f"JSON 解析失敗: {str(e)}", line_number)
            return None
        except Exception as e:
            # 其他未預期的錯誤，記錄錯誤並跳過該行
            if self.error_handler:
                self.error_handler.log_error("unexpected_error", f"未預期錯誤: {str(e)}", line_number)
            return None

    def extract_rendered_message(self, log_data: Dict[str, Any]) -> Optional[str]:
        """
        從解析後的 JSON 中提取 RenderedMessage 欄位

        Args:
            log_data: 解析後的日誌資料字典

        Returns:
            RenderedMessage 字串，如果欄位不存在則返回 None
        """
        try:
            rendered_message = log_data.get('RenderedMessage')
            if rendered_message and isinstance(rendered_message, str):
                return rendered_message
            return None

        except (AttributeError, TypeError) as e:
            # 如果 log_data 不是字典或其他型別錯誤
            if self.error_handler:
                self.error_handler.log_error("field_extraction_error", f"提取 RenderedMessage 失敗: {str(e)}")
            return None

    def extract_timestamp(self, log_data: Dict[str, Any]) -> Optional[datetime]:
        """
        從解析後的 JSON 中提取並轉換時間戳記

        Args:
            log_data: 解析後的日誌資料字典

        Returns:
            轉換後的 datetime 物件，如果提取或轉換失敗則返回 None
        """
        try:
            timestamp_str = log_data.get('Timestamp')
            if not timestamp_str or not isinstance(timestamp_str, str):
                return None

            # 解析 ISO 8601 格式的時間戳記
            # 處理可能的格式變化，如有無微秒、時區資訊等
            timestamp_str = timestamp_str.strip()

            # 嘗試解析不同的 ISO 8601 格式
            formats_to_try = [
                '%Y-%m-%dT%H:%M:%S.%fZ',      # 2023-01-01T12:00:00.123456Z
                '%Y-%m-%dT%H:%M:%SZ',         # 2023-01-01T12:00:00Z
                '%Y-%m-%dT%H:%M:%S.%f',       # 2023-01-01T12:00:00.123456
                '%Y-%m-%dT%H:%M:%S',          # 2023-01-01T12:00:00
                '%Y-%m-%dT%H:%M:%S%z',        # 2023-01-01T12:00:00+0800
                '%Y-%m-%dT%H:%M:%S.%f%z',     # 2023-01-01T12:00:00.123456+0800
            ]

            for fmt in formats_to_try:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue

            # 如果所有格式都失敗，嘗試使用 fromisoformat (Python 3.7+)
            try:
                # 移除 'Z' 並替換為 '+00:00' 以符合 fromisoformat 格式
                if timestamp_str.endswith('Z'):
                    timestamp_str = timestamp_str[:-1] + '+00:00'
                return datetime.fromisoformat(timestamp_str)
            except ValueError:
                return None

        except (AttributeError, TypeError, ValueError) as e:
            # 各種可能的錯誤情況
            if self.error_handler:
                self.error_handler.log_error("timestamp_parse_error", f"時間戳記解析失敗: {str(e)}")
            return None


class LogFileReader:
    """日誌檔案讀取器，負責檔案讀取和基本錯誤處理"""

    def __init__(self, file_path: str, error_handler: Optional['ErrorHandler'] = None):
        """
        初始化 LogFileReader

        Args:
            file_path: 要讀取的日誌檔案路徑
            error_handler: 錯誤處理器實例（可選）
        """
        self.file_path = file_path
        self.error_handler = error_handler

    def is_file_accessible(self) -> bool:
        """
        檢查檔案是否存在且可讀取

        Returns:
            如果檔案存在且可讀取則返回 True，否則返回 False
        """
        try:
            # 檢查檔案是否存在
            if not os.path.exists(self.file_path):
                if self.error_handler:
                    self.error_handler.log_error("file_not_found", f"檔案不存在: {self.file_path}")
                return False

            # 檢查是否為檔案（而非目錄）
            if not os.path.isfile(self.file_path):
                if self.error_handler:
                    self.error_handler.log_error("not_a_file", f"指定的路徑不是檔案: {self.file_path}")
                return False

            # 檢查是否有讀取權限
            if not os.access(self.file_path, os.R_OK):
                if self.error_handler:
                    self.error_handler.log_error("permission_denied", f"沒有讀取權限: {self.file_path}")
                return False

            return True

        except (OSError, IOError) as e:
            # 處理系統相關的錯誤
            if self.error_handler:
                self.error_handler.log_error("file_access_error", f"檔案存取錯誤: {str(e)}")
            return False
        except Exception as e:
            # 處理其他未預期的錯誤
            if self.error_handler:
                self.error_handler.log_error("unexpected_error", f"檔案檢查時發生未預期錯誤: {str(e)}")
            return False

    def read_lines(self) -> Iterator[str]:
        """
        使用迭代器逐行讀取檔案內容

        Returns:
            字串迭代器，每次返回檔案的一行內容

        Raises:
            FileNotFoundError: 當檔案不存在時
            PermissionError: 當沒有讀取權限時
            IOError: 當發生 I/O 錯誤時
        """
        # 首先檢查檔案是否可存取
        if not os.path.exists(self.file_path):
            error_msg = f"檔案不存在: {self.file_path}"
            if self.error_handler:
                self.error_handler.log_error("file_not_found", error_msg)
            raise FileNotFoundError(error_msg)

        if not os.path.isfile(self.file_path):
            error_msg = f"指定的路徑不是檔案: {self.file_path}"
            if self.error_handler:
                self.error_handler.log_error("not_a_file", error_msg)
            raise IOError(error_msg)

        if not os.access(self.file_path, os.R_OK):
            error_msg = f"沒有讀取權限: {self.file_path}"
            if self.error_handler:
                self.error_handler.log_error("permission_denied", error_msg)
            raise PermissionError(error_msg)

        try:
            # 使用 with 語句確保檔案會被正確關閉
            # 使用 utf-8 編碼讀取檔案，並處理編碼錯誤
            with open(self.file_path, 'r', encoding='utf-8', errors='replace') as file:
                for line in file:
                    yield line

        except FileNotFoundError as e:
            # 檔案在檢查後被刪除的情況
            error_msg = f"檔案不存在: {self.file_path}"
            if self.error_handler:
                self.error_handler.log_error("file_not_found", error_msg)
            raise FileNotFoundError(error_msg)
        except PermissionError as e:
            # 權限在檢查後被改變的情況
            error_msg = f"沒有讀取權限: {self.file_path}"
            if self.error_handler:
                self.error_handler.log_error("permission_denied", error_msg)
            raise PermissionError(error_msg)
        except UnicodeDecodeError as e:
            # 編碼錯誤（雖然使用了 errors='replace'，但仍可能發生）
            error_msg = f"檔案編碼錯誤: {self.file_path} - {str(e)}"
            if self.error_handler:
                self.error_handler.log_error("encoding_error", error_msg)
            raise IOError(error_msg)
        except IOError as e:
            # 其他 I/O 相關錯誤
            error_msg = f"讀取檔案時發生錯誤: {self.file_path} - {str(e)}"
            if self.error_handler:
                self.error_handler.log_error("io_error", error_msg)
            raise IOError(error_msg)
        except Exception as e:
            # 處理其他未預期的錯誤
            error_msg = f"讀取檔案時發生未預期錯誤: {self.file_path} - {str(e)}"
            if self.error_handler:
                self.error_handler.log_error("unexpected_error", error_msg)
            raise IOError(error_msg)


class HTTPInfoExtractor:
    """HTTP 資訊提取器，負責從 RenderedMessage 中提取 HTTP 請求資訊"""

    def __init__(self, error_handler: Optional['ErrorHandler'] = None):
        """
        初始化 HTTPInfoExtractor

        Args:
            error_handler: 錯誤處理器實例（可選）
        """
        # 使用預編譯的正規表示式以提高效能
        self.http_pattern = HTTP_PATTERN
        self.error_handler = error_handler

    def extract_http_info(self, rendered_message: str) -> Optional[HTTPRequestInfo]:
        """
        從 RenderedMessage 中提取 HTTP 請求資訊

        Args:
            rendered_message: 從日誌中提取的 RenderedMessage 字串

        Returns:
            HTTPRequestInfo 物件，如果提取失敗則返回 None
        """
        if not rendered_message or not isinstance(rendered_message, str):
            return None

        try:
            # 使用預編譯的正規表示式進行匹配
            match = self.http_pattern.search(rendered_message)

            if not match:
                # 正規表示式不匹配，記錄錯誤並返回 None
                if self.error_handler:
                    self.error_handler.log_error("regex_mismatch", f"HTTP 訊息格式不匹配: {rendered_message}")
                return None

            # 提取匹配的群組
            method = match.group(1)
            path = match.group(2)
            status_code_str = match.group(3)
            elapsed_time_str = match.group(4)

            # 轉換資料型別
            try:
                status_code = int(status_code_str)
                elapsed_time = float(elapsed_time_str)
            except (ValueError, TypeError) as e:
                # 數值轉換失敗
                if self.error_handler:
                    self.error_handler.log_error("data_conversion_error", f"數值轉換失敗: {str(e)}")
                return None

            # 建立並返回 HTTPRequestInfo 物件
            return HTTPRequestInfo(
                method=method,
                path=path,
                status_code=status_code,
                elapsed_time=elapsed_time
            )

        except (AttributeError, TypeError) as e:
            # 處理其他可能的錯誤
            if self.error_handler:
                self.error_handler.log_error("http_extraction_error", f"HTTP 資訊提取錯誤: {str(e)}")
            return None
        except Exception as e:
            # 處理未預期的錯誤
            if self.error_handler:
                self.error_handler.log_error("unexpected_error", f"未預期錯誤: {str(e)}")
            return None


class DataAggregator:
    """資料彙總器，負責彙總和統計 HTTP 請求資料"""

    def __init__(self, time_filter: Optional[TimeRangeFilter] = None):
        """
        初始化 DataAggregator

        Args:
            time_filter: 可選的時間範圍過濾器
        """
        # 使用 defaultdict 來儲存每個 endpoint 的統計資料
        # 鍵為 (method, path) 的元組，值為 EndpointStats 物件
        self.endpoint_stats: Dict[Tuple[str, str], EndpointStats] = defaultdict(EndpointStats)

        # 時間範圍過濾器
        self.time_filter = time_filter

        # 追蹤時間範圍
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None

        # 追蹤過濾統計
        self.total_requests_processed = 0
        self.filtered_out_requests = 0

    def add_request(self, request_info: HTTPRequestInfo, timestamp: Optional[datetime] = None) -> bool:
        """
        累計 endpoint 統計資料

        Args:
            request_info: HTTP 請求資訊
            timestamp: 請求的時間戳記（可選）

        Returns:
            如果請求被成功添加則返回 True，如果被時間過濾器過濾則返回 False
        """
        if not request_info:
            return False

        self.total_requests_processed += 1

        try:
            # 檢查是否在時間範圍內
            if self.time_filter and not self.time_filter.is_in_range(timestamp):
                self.filtered_out_requests += 1
                return False

            # 建立 endpoint 識別鍵
            endpoint_key = (request_info.method, request_info.path)

            # 取得或建立該 endpoint 的統計資料
            stats = self.endpoint_stats[endpoint_key]

            # 更新請求計數
            stats.count += 1

            # 更新狀態碼統計
            if request_info.status_code in stats.status_codes:
                stats.status_codes[request_info.status_code] += 1
            else:
                stats.status_codes[request_info.status_code] = 1

            # 記錄回應時間
            stats.elapsed_times.append(request_info.elapsed_time)

            # 更新時間範圍
            if timestamp:
                self._update_time_range(timestamp)

            return True

        except Exception:
            # 處理未預期的錯誤，但不中斷處理流程
            return False

    def get_aggregated_data(self) -> Dict[Tuple[str, str], EndpointStats]:
        """
        取得彙總後的資料

        Returns:
            包含所有 endpoint 統計資料的字典
        """
        return dict(self.endpoint_stats)

    def get_time_range(self) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        取得日誌的時間範圍

        Returns:
            包含開始時間和結束時間的元組
        """
        return (self.start_time, self.end_time)

    def get_filter_statistics(self) -> Dict[str, int]:
        """
        取得過濾統計資訊

        Returns:
            包含過濾統計的字典
        """
        return {
            'total_processed': self.total_requests_processed,
            'filtered_out': self.filtered_out_requests,
            'included': self.total_requests_processed - self.filtered_out_requests
        }

    def _update_time_range(self, timestamp: datetime) -> None:
        """
        更新時間範圍追蹤

        Args:
            timestamp: 新的時間戳記
        """
        try:
            # 更新開始時間（取最早的時間）
            if self.start_time is None or timestamp < self.start_time:
                self.start_time = timestamp

            # 更新結束時間（取最晚的時間）
            if self.end_time is None or timestamp > self.end_time:
                self.end_time = timestamp

        except (TypeError, AttributeError):
            # 處理時間比較可能的錯誤
            pass


class StatisticsCalculator:
    """統計計算器，負責計算統計資料，如平均回應時間"""

    @staticmethod
    def calculate_average_elapsed(elapsed_times: List[float]) -> float:
        """
        計算平均回應時間

        Args:
            elapsed_times: 回應時間列表

        Returns:
            平均回應時間，如果列表為空則返回 0.0
        """
        if not elapsed_times:
            return 0.0

        try:
            # 計算總回應時間 / 總請求次數
            total_time = sum(elapsed_times)
            count = len(elapsed_times)

            if count == 0:
                return 0.0

            return total_time / count

        except (TypeError, ZeroDivisionError):
            # 處理可能的錯誤情況
            return 0.0
        except Exception:
            # 處理其他未預期的錯誤
            return 0.0

    @staticmethod
    def format_status_code_distribution(status_codes: Dict[int, int]) -> str:
        """
        格式化狀態碼分佈

        Args:
            status_codes: 狀態碼統計字典

        Returns:
            格式化後的狀態碼分佈字串
        """
        if not status_codes:
            return "無資料"

        try:
            # 按狀態碼排序
            sorted_codes = sorted(status_codes.items())

            # 格式化為 "200: 10, 404: 2" 的形式
            formatted_parts = []
            for status_code, count in sorted_codes:
                formatted_parts.append(f"{status_code}: {count}")

            return ", ".join(formatted_parts)

        except (TypeError, AttributeError):
            # 處理可能的錯誤情況
            return "格式化錯誤"
        except Exception:
            # 處理其他未預期的錯誤
            return "未知錯誤"


class ResultFormatter:
    """結果格式化器，負責格式化輸出結果"""

    def format_results(self,
                      aggregated_data: Dict[Tuple[str, str], EndpointStats],
                      time_range: Tuple[Optional[datetime], Optional[datetime]],
                      file_info: Optional[Dict[str, Any]] = None) -> str:
        """
        以表格形式格式化結果

        Args:
            aggregated_data: 彙總後的資料
            time_range: 時間範圍元組 (開始時間, 結束時間)
            file_info: 檔案處理資訊（可選），包含檔案數、成功數、失敗數等

        Returns:
            格式化後的結果字串
        """
        if not aggregated_data:
            return "沒有找到有效的日誌資料。"

        try:
            result_lines = []

            # 添加標題
            result_lines.append("JKoPay API 日誌分析結果")
            result_lines.append("=" * 50)
            result_lines.append("")

            # 添加檔案處理資訊（如果有多檔案）
            if file_info:
                result_lines.append(f"處理檔案數: {file_info.get('total_files', 0)}")
                if file_info.get('processed_files', 0) != file_info.get('total_files', 0):
                    result_lines.append(f"成功處理: {file_info.get('processed_files', 0)}")
                    result_lines.append(f"處理失敗: {file_info.get('failed_files', 0)}")
                result_lines.append("")

            # 添加時間範圍資訊
            time_range_str = self.format_time_range(time_range[0], time_range[1])
            result_lines.append(f"分析時間範圍: {time_range_str}")
            result_lines.append("")

            # 計算所有統計資料的平均回應時間
            for stats in aggregated_data.values():
                stats.calculate_average_elapsed()

            # 建立表格標題
            result_lines.append("API Endpoint 統計資料:")
            result_lines.append("-" * 100)
            result_lines.append(f"{'方法':<8} {'路徑':<30} {'請求數':<8} {'狀態碼分佈':<25} {'平均回應時間(ms)':<15}")
            result_lines.append("-" * 100)

            # 按 endpoint 排序（先按方法，再按路徑）
            sorted_endpoints = sorted(aggregated_data.items(),
                                    key=lambda x: (x[0][0], x[0][1]))

            # 添加每個 endpoint 的資料
            for (method, path), stats in sorted_endpoints:
                # 格式化狀態碼分佈
                status_distribution = StatisticsCalculator.format_status_code_distribution(stats.status_codes)

                # 格式化平均回應時間（保留 2 位小數）
                avg_time = f"{stats.average_elapsed:.2f}"

                # 截斷過長的路徑
                display_path = path if len(path) <= 30 else path[:27] + "..."

                # 添加資料行
                result_lines.append(
                    f"{method:<8} {display_path:<30} {stats.count:<8} {status_distribution:<25} {avg_time:<15}"
                )

            result_lines.append("-" * 100)
            result_lines.append("")

            # 添加總結資訊
            total_requests = sum(stats.count for stats in aggregated_data.values())
            total_endpoints = len(aggregated_data)
            result_lines.append(f"總計: {total_endpoints} 個 API endpoints，{total_requests} 個請求")

            return "\n".join(result_lines)

        except Exception as e:
            return f"格式化結果時發生錯誤: {str(e)}"

    def format_time_range(self, start_time: Optional[datetime], end_time: Optional[datetime]) -> str:
        """
        格式化時間範圍顯示

        Args:
            start_time: 開始時間
            end_time: 結束時間

        Returns:
            格式化後的時間範圍字串
        """
        try:
            if start_time is None and end_time is None:
                return "無法確定時間範圍"

            if start_time is None:
                return f"結束於 {end_time.strftime('%Y-%m-%d %H:%M:%S')}"

            if end_time is None:
                return f"開始於 {start_time.strftime('%Y-%m-%d %H:%M:%S')}"

            # 計算時間差
            duration = end_time - start_time
            duration_str = self._format_duration(duration)

            return (f"{start_time.strftime('%Y-%m-%d %H:%M:%S')} 至 "
                   f"{end_time.strftime('%Y-%m-%d %H:%M:%S')} "
                   f"(持續時間: {duration_str})")

        except (AttributeError, TypeError):
            return "時間格式錯誤"
        except Exception:
            return "時間範圍格式化錯誤"

    def _format_duration(self, duration) -> str:
        """
        格式化持續時間

        Args:
            duration: timedelta 物件

        Returns:
            格式化後的持續時間字串
        """
        try:
            total_seconds = int(duration.total_seconds())

            if total_seconds < 60:
                return f"{total_seconds} 秒"
            elif total_seconds < 3600:
                minutes = total_seconds // 60
                seconds = total_seconds % 60
                return f"{minutes} 分 {seconds} 秒"
            elif total_seconds < 86400:
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                return f"{hours} 小時 {minutes} 分"
            else:
                days = total_seconds // 86400
                hours = (total_seconds % 86400) // 3600
                return f"{days} 天 {hours} 小時"

        except (AttributeError, TypeError):
            return "未知"
        except Exception:
            return "計算錯誤"


class ErrorHandler:
    """錯誤處理器，負責記錄各種錯誤類型並提供錯誤統計"""

    def __init__(self, verbose: bool = False):
        """
        初始化 ErrorHandler

        Args:
            verbose: 是否顯示詳細錯誤訊息
        """
        self.verbose = verbose
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.error_messages: List[str] = []
        self.total_lines_processed = 0

    def log_error(self, error_type: str, message: str, line_number: Optional[int] = None) -> None:
        """
        記錄各種錯誤類型

        Args:
            error_type: 錯誤類型（如 'json_parse_error', 'regex_mismatch', 'file_error'）
            message: 錯誤訊息
            line_number: 發生錯誤的行號（可選）
        """
        try:
            # 更新錯誤計數
            self.error_counts[error_type] += 1

            # 建立詳細錯誤訊息
            if line_number is not None:
                detailed_message = f"[行 {line_number}] {error_type}: {message}"
            else:
                detailed_message = f"{error_type}: {message}"

            # 儲存錯誤訊息
            self.error_messages.append(detailed_message)

            # 如果啟用詳細模式，立即輸出錯誤訊息
            if self.verbose:
                print(f"錯誤: {detailed_message}")

        except Exception:
            # 避免錯誤處理器本身出錯
            pass

    def increment_processed_lines(self) -> None:
        """增加已處理行數計數"""
        self.total_lines_processed += 1

    def get_error_summary(self) -> Dict[str, int]:
        """
        取得錯誤統計

        Returns:
            包含各種錯誤類型及其計數的字典
        """
        return dict(self.error_counts)

    def get_total_errors(self) -> int:
        """
        取得總錯誤數

        Returns:
            總錯誤數
        """
        return sum(self.error_counts.values())

    def get_error_rate(self) -> float:
        """
        計算錯誤率

        Returns:
            錯誤率（錯誤數 / 總處理行數）
        """
        if self.total_lines_processed == 0:
            return 0.0

        return self.get_total_errors() / self.total_lines_processed

    def has_errors(self) -> bool:
        """
        檢查是否有錯誤

        Returns:
            如果有錯誤則返回 True，否則返回 False
        """
        return self.get_total_errors() > 0

    def print_error_summary(self) -> None:
        """列印錯誤摘要"""
        try:
            total_errors = self.get_total_errors()

            if total_errors == 0:
                print("處理過程中沒有發生錯誤。")
                return

            print(f"\n錯誤摘要:")
            print(f"總處理行數: {self.total_lines_processed}")
            print(f"總錯誤數: {total_errors}")
            print(f"錯誤率: {self.get_error_rate():.2%}")
            print("\n錯誤類型分佈:")

            for error_type, count in sorted(self.error_counts.items()):
                percentage = (count / total_errors) * 100
                print(f"  {error_type}: {count} ({percentage:.1f}%)")

            if self.verbose and self.error_messages:
                print(f"\n詳細錯誤訊息 (最近 10 筆):")
                for message in self.error_messages[-10:]:
                    print(f"  {message}")

        except Exception:
            print("列印錯誤摘要時發生錯誤。")

    def clear_errors(self) -> None:
        """清除所有錯誤記錄"""
        self.error_counts.clear()
        self.error_messages.clear()
        self.total_lines_processed = 0


def process_single_file(file_path: str, json_parser: JSONLogParser, http_extractor: HTTPInfoExtractor,
                       data_aggregator: DataAggregator, error_handler: ErrorHandler) -> Tuple[int, int]:
    """
    處理單個日誌檔案

    Args:
        file_path: 檔案路徑
        json_parser: JSON 解析器
        http_extractor: HTTP 資訊提取器
        data_aggregator: 資料彙總器
        error_handler: 錯誤處理器

    Returns:
        處理的行數和有效請求數的元組

    Raises:
        FileNotFoundError: 檔案不存在
        PermissionError: 沒有讀取權限
        IOError: I/O 錯誤
    """
    file_reader = LogFileReader(file_path, error_handler)

    # 檢查檔案是否可存取
    if not file_reader.is_file_accessible():
        raise IOError(f"無法存取檔案 '{file_path}'")

    file_line_number = 0
    file_valid_requests = 0

    for line in file_reader.read_lines():
        file_line_number += 1
        error_handler.increment_processed_lines()

        # 解析 JSON
        log_data = json_parser.parse_log_line(line, file_line_number)
        if log_data is None:
            continue

        # 提取 RenderedMessage
        rendered_message = json_parser.extract_rendered_message(log_data)
        if rendered_message is None:
            continue

        # 提取 HTTP 資訊
        http_info = http_extractor.extract_http_info(rendered_message)
        if http_info is None:
            continue

        # 提取時間戳記
        timestamp = json_parser.extract_timestamp(log_data)

        # 彙總資料
        if data_aggregator.add_request(http_info, timestamp):
            file_valid_requests += 1

    return file_line_number, file_valid_requests


def parse_datetime(datetime_str: str) -> datetime:
    """
    解析時間字串為 datetime 物件

    Args:
        datetime_str: 時間字串，支援多種格式

    Returns:
        解析後的 datetime 物件

    Raises:
        argparse.ArgumentTypeError: 如果無法解析時間字串
    """
    from datetime import timezone, timedelta

    # 定義台灣時區 (UTC+8)
    taiwan_tz = timezone(timedelta(hours=8))

    formats_to_try = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%dT%H:%M:%S.%f",
        "%Y-%m-%dT%H:%M:%S%z",
        "%Y-%m-%dT%H:%M:%S.%f%z"
    ]

    for fmt in formats_to_try:
        try:
            parsed_dt = datetime.strptime(datetime_str.strip(), fmt)
            # 如果解析的時間沒有時區資訊，假設為台灣時區
            if parsed_dt.tzinfo is None:
                parsed_dt = parsed_dt.replace(tzinfo=taiwan_tz)
            return parsed_dt
        except ValueError:
            continue

    # 嘗試使用 fromisoformat (Python 3.7+)
    try:
        if datetime_str.endswith('Z'):
            datetime_str = datetime_str[:-1] + '+00:00'
        parsed_dt = datetime.fromisoformat(datetime_str.strip())
        # 如果解析的時間沒有時區資訊，假設為台灣時區
        if parsed_dt.tzinfo is None:
            parsed_dt = parsed_dt.replace(tzinfo=taiwan_tz)
        return parsed_dt
    except ValueError:
        pass

    raise argparse.ArgumentTypeError(f"無法解析時間格式: {datetime_str}")


def main() -> None:
    """主程式入口點"""
    import sys

    # 設定命令行參數解析器
    parser = argparse.ArgumentParser(
        description='JKoPay API 日誌分析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''使用範例:
  python analyze_logs.py jkopay-api-onlinepay.txt
  python analyze_logs.py file1.txt file2.txt file3.txt
  python analyze_logs.py *.txt --start-time "2025-08-07 10:00:00"
  python analyze_logs.py jkopay-api-onlinepay.txt --start-time "2025-08-07 10:00" --end-time "2025-08-07 11:00"
  python analyze_logs.py jkopay-api-onlinepay.txt --end-time "2025-08-07T10:50:00"
        '''
    )

    parser.add_argument('log_files', nargs='+', help='日誌檔案路徑（可指定多個檔案）')
    parser.add_argument(
        '--start-time',
        type=parse_datetime,
        help='開始時間（包含），支援多種格式如 "2025-08-07 10:00:00" 或 "2025-08-07T10:00:00"'
    )
    parser.add_argument(
        '--end-time',
        type=parse_datetime,
        help='結束時間（包含），支援多種格式如 "2025-08-07 11:00:00" 或 "2025-08-07T11:00:00"'
    )

    # 解析參數
    args = parser.parse_args()

    # 驗證時間範圍
    if args.start_time and args.end_time and args.start_time >= args.end_time:
        print("錯誤: 開始時間必須早於結束時間")
        sys.exit(1)

    log_file_paths = args.log_files

    # 建立時間範圍過濾器
    time_filter = None
    if args.start_time or args.end_time:
        time_filter = TimeRangeFilter(args.start_time, args.end_time)
        print(f"時間範圍過濾: {time_filter.get_description()}")

    # 建立錯誤處理器
    error_handler = ErrorHandler(verbose=False)

    try:
        # 建立各個組件
        json_parser = JSONLogParser(error_handler)
        http_extractor = HTTPInfoExtractor(error_handler)
        data_aggregator = DataAggregator(time_filter)
        result_formatter = ResultFormatter()

        print(f"開始分析 {len(log_file_paths)} 個日誌檔案:")
        for i, path in enumerate(log_file_paths, 1):
            print(f"  {i}. {path}")
        print("正在處理...")

        # 處理多個日誌檔案
        total_line_number = 0
        total_valid_requests = 0
        processed_files = 0
        failed_files = []

        try:
            for file_index, log_file_path in enumerate(log_file_paths, 1):
                print(f"\n處理檔案 {file_index}/{len(log_file_paths)}: {log_file_path}")

                try:
                    file_lines, file_requests = process_single_file(
                        log_file_path, json_parser, http_extractor,
                        data_aggregator, error_handler
                    )

                    total_line_number += file_lines
                    total_valid_requests += file_requests
                    processed_files += 1

                    print(f"檔案 {log_file_path}: 處理 {file_lines} 行，找到 {file_requests} 個有效請求")

                    # 每處理完一個檔案顯示累計進度
                    print(f"累計進度: {total_line_number} 行，{total_valid_requests} 個有效請求")

                except (FileNotFoundError, PermissionError, IOError) as e:
                    failed_files.append((log_file_path, str(e)))
                    print(f"檔案 {log_file_path} 處理失敗: {str(e)}")
                    continue
                except Exception as e:
                    failed_files.append((log_file_path, f"未預期錯誤: {str(e)}"))
                    print(f"檔案 {log_file_path} 處理失敗: 未預期錯誤: {str(e)}")
                    continue

        except KeyboardInterrupt:
            print("\n處理被使用者中斷。")
            sys.exit(1)

        # 顯示處理結果摘要
        print(f"\n處理摘要:")
        print(f"  總檔案數: {len(log_file_paths)}")
        print(f"  成功處理: {processed_files}")
        print(f"  處理失敗: {len(failed_files)}")
        if failed_files:
            print("  失敗檔案:")
            for file_path, error_msg in failed_files:
                print(f"    {file_path}: {error_msg}")

        if processed_files == 0:
            print("錯誤: 沒有成功處理任何檔案")
            if error_handler.has_errors():
                error_handler.print_error_summary()
            sys.exit(1)

        print(f"\n所有檔案處理完成！總共處理 {total_line_number} 行，找到 {total_valid_requests} 個有效請求。")

        # 顯示時間過濾統計（如果有過濾）
        if time_filter and time_filter.has_filter():
            filter_stats = data_aggregator.get_filter_statistics()
            print(f"時間範圍過濾結果:")
            print(f"  總處理請求數: {filter_stats['total_processed']}")
            print(f"  過濾排除數: {filter_stats['filtered_out']}")
            print(f"  包含在範圍內: {filter_stats['included']}")
            if filter_stats['total_processed'] > 0:
                inclusion_rate = (filter_stats['included'] / filter_stats['total_processed']) * 100
                print(f"  包含率: {inclusion_rate:.1f}%")

        # 取得彙總資料
        aggregated_data = data_aggregator.get_aggregated_data()
        time_range = data_aggregator.get_time_range()

        if not aggregated_data:
            print("沒有找到有效的 HTTP 請求資料。")
            if error_handler.has_errors():
                print("\n可能的原因:")
                error_handler.print_error_summary()
            sys.exit(0)

        # 格式化並顯示結果
        print("\n" + "=" * 60)

        # 準備檔案資訊（如果有多個檔案）
        file_info = None
        if len(log_file_paths) > 1:
            file_info = {
                'total_files': len(log_file_paths),
                'processed_files': processed_files,
                'failed_files': len(failed_files)
            }

        result = result_formatter.format_results(aggregated_data, time_range, file_info)
        print(result)

        # 顯示錯誤摘要（如果有錯誤）
        if error_handler.has_errors():
            print("\n" + "=" * 60)
            error_handler.print_error_summary()

        print(f"\n分析完成！成功處理了 {len(aggregated_data)} 個不同的 API endpoints。")

    except Exception as e:
        print(f"程式執行時發生嚴重錯誤: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
