"""
ErrorHandler 類別的單元測試

測試錯誤記錄、統計和整合功能。
"""

import unittest
from analyze_logs import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, JSONLog<PERSON><PERSON>er, HTTPInfoExtractor, LogFileReader


class TestErrorHandler(unittest.TestCase):
    """測試 ErrorHandler 類別"""

    def setUp(self):
        """設定測試環境"""
        self.error_handler = ErrorHandler()

    def test_log_error_basic(self):
        """測試基本錯誤記錄"""
        self.error_handler.log_error("test_error", "測試錯誤訊息")

        summary = self.error_handler.get_error_summary()
        self.assertEqual(summary["test_error"], 1)
        self.assertEqual(self.error_handler.get_total_errors(), 1)

    def test_log_error_with_line_number(self):
        """測試帶行號的錯誤記錄"""
        self.error_handler.log_error("json_error", "JSON 解析失敗", 10)

        summary = self.error_handler.get_error_summary()
        self.assertEqual(summary["json_error"], 1)
        self.assertTrue(any("行 10" in msg for msg in self.error_handler.error_messages))

    def test_multiple_errors_same_type(self):
        """測試相同類型的多個錯誤"""
        for i in range(3):
            self.error_handler.log_error("json_error", f"錯誤 {i}")

        summary = self.error_handler.get_error_summary()
        self.assertEqual(summary["json_error"], 3)
        self.assertEqual(self.error_handler.get_total_errors(), 3)

    def test_multiple_error_types(self):
        """測試不同類型的錯誤"""
        self.error_handler.log_error("json_error", "JSON 錯誤")
        self.error_handler.log_error("regex_error", "正規表示式錯誤")
        self.error_handler.log_error("json_error", "另一個 JSON 錯誤")

        summary = self.error_handler.get_error_summary()
        self.assertEqual(summary["json_error"], 2)
        self.assertEqual(summary["regex_error"], 1)
        self.assertEqual(self.error_handler.get_total_errors(), 3)

    def test_error_rate_calculation(self):
        """測試錯誤率計算"""
        # 設定處理行數
        for _ in range(10):
            self.error_handler.increment_processed_lines()

        # 添加錯誤
        for _ in range(2):
            self.error_handler.log_error("test_error", "測試錯誤")

        error_rate = self.error_handler.get_error_rate()
        self.assertEqual(error_rate, 0.2)  # 2/10 = 0.2

    def test_error_rate_no_lines_processed(self):
        """測試沒有處理行數時的錯誤率"""
        self.error_handler.log_error("test_error", "測試錯誤")
        error_rate = self.error_handler.get_error_rate()
        self.assertEqual(error_rate, 0.0)

    def test_has_errors(self):
        """測試錯誤檢查"""
        self.assertFalse(self.error_handler.has_errors())

        self.error_handler.log_error("test_error", "測試錯誤")
        self.assertTrue(self.error_handler.has_errors())

    def test_clear_errors(self):
        """測試清除錯誤"""
        self.error_handler.log_error("test_error", "測試錯誤")
        self.error_handler.increment_processed_lines()

        self.assertTrue(self.error_handler.has_errors())
        self.assertEqual(self.error_handler.total_lines_processed, 1)

        self.error_handler.clear_errors()

        self.assertFalse(self.error_handler.has_errors())
        self.assertEqual(self.error_handler.total_lines_processed, 0)
        self.assertEqual(len(self.error_handler.error_messages), 0)

    def test_verbose_mode(self):
        """測試詳細模式（這個測試主要確保不會出錯）"""
        verbose_handler = ErrorHandler(verbose=True)
        verbose_handler.log_error("test_error", "測試錯誤")
        # 在詳細模式下，錯誤會被列印，但我們無法在單元測試中捕獲 print 輸出
        # 所以只是確保不會出錯
        self.assertEqual(verbose_handler.get_total_errors(), 1)


class TestErrorHandlerIntegration(unittest.TestCase):
    """測試錯誤處理器與其他組件的整合"""

    def setUp(self):
        """設定測試環境"""
        self.error_handler = ErrorHandler()

    def test_json_parser_integration(self):
        """測試 JSONLogParser 與錯誤處理器的整合"""
        parser = JSONLogParser(self.error_handler)

        # 測試無效 JSON
        result = parser.parse_log_line('{"invalid": json}', 1)
        self.assertIsNone(result)
        self.assertTrue(self.error_handler.has_errors())

        summary = self.error_handler.get_error_summary()
        self.assertIn("json_parse_error", summary)

    def test_json_parser_empty_line(self):
        """測試 JSONLogParser 處理空行"""
        parser = JSONLogParser(self.error_handler)

        result = parser.parse_log_line('', 1)
        self.assertIsNone(result)

        summary = self.error_handler.get_error_summary()
        self.assertIn("empty_line", summary)

    def test_http_extractor_integration(self):
        """測試 HTTPInfoExtractor 與錯誤處理器的整合"""
        extractor = HTTPInfoExtractor(self.error_handler)

        # 測試不匹配的訊息
        result = extractor.extract_http_info("Invalid HTTP message format")
        self.assertIsNone(result)
        self.assertTrue(self.error_handler.has_errors())

        summary = self.error_handler.get_error_summary()
        self.assertIn("regex_mismatch", summary)

    def test_http_extractor_valid_message(self):
        """測試 HTTPInfoExtractor 處理有效訊息"""
        extractor = HTTPInfoExtractor(self.error_handler)

        # 測試有效訊息
        result = extractor.extract_http_info('HTTP "GET" "/api/users" responded 200 in 150.5 ms')
        self.assertIsNotNone(result)
        self.assertFalse(self.error_handler.has_errors())

    def test_log_file_reader_nonexistent_file(self):
        """測試 LogFileReader 處理不存在的檔案"""
        reader = LogFileReader("nonexistent_file.txt", self.error_handler)

        # 檢查檔案存取性
        accessible = reader.is_file_accessible()
        self.assertFalse(accessible)
        self.assertTrue(self.error_handler.has_errors())

        summary = self.error_handler.get_error_summary()
        self.assertIn("file_not_found", summary)

    def test_multiple_components_error_accumulation(self):
        """測試多個組件的錯誤累積"""
        parser = JSONLogParser(self.error_handler)
        extractor = HTTPInfoExtractor(self.error_handler)

        # 產生不同類型的錯誤
        parser.parse_log_line('invalid json', 1)
        extractor.extract_http_info("invalid message")
        parser.parse_log_line('', 2)

        # 檢查錯誤累積
        self.assertEqual(self.error_handler.get_total_errors(), 3)

        summary = self.error_handler.get_error_summary()
        self.assertIn("json_parse_error", summary)
        self.assertIn("regex_mismatch", summary)
        self.assertIn("empty_line", summary)


if __name__ == '__main__':
    unittest.main()
