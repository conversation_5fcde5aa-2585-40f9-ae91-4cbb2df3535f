# 使用範例

本文件提供 JKoPay API 日誌分析工具的詳細使用範例，包括新的多檔案處理和時間範圍過濾功能。

## 基本使用範例

### 範例 1: 分析單一日誌檔案

```bash
python analyze_logs.py jkopay-api-onlinepay.txt
```

**輸出範例**:
```
開始分析 1 個日誌檔案:
  1. jkopay-api-onlinepay.txt
正在處理...

處理檔案 1/1: jkopay-api-onlinepay.txt
檔案 jkopay-api-onlinepay.txt: 處理 4256 行，找到 1618 個有效請求
累計進度: 4256 行，1618 個有效請求

處理摘要:
  總檔案數: 1
  成功處理: 1
  處理失敗: 0

所有檔案處理完成！總共處理 4256 行，找到 1618 個有效請求。

============================================================
JKoPay API 日誌分析結果
==================================================

分析時間範圍: 2025-08-07 10:49:40 至 2025-08-07 10:58:39 (持續時間: 8 分 58 秒)

API Endpoint 統計資料:
----------------------------------------------------------------------------------------------------
方法       路徑                             請求數      狀態碼分佈                     平均回應時間(ms)     
----------------------------------------------------------------------------------------------------
GET      /external/qr                   8        200: 8                    54.36          
GET      /external/service-r/knock      71       200: 71                   9.95           
GET      /platform/inquiry              802      200: 802                  13.08          
GET      /platform_reimburse/reimbur... 7        200: 7                    6.52           
GET      /platform_reimburse/store/b... 4        200: 4                    51.02          
GET      /web/paymentRouter             218      302: 218                  2.08           
OPTIONS  /external/service-r/resolve... 11       204: 11                   0.05           
POST     /cod-endpoint/sf/payment       1        200: 1                    3.80           
POST     /consumerApp/foreignExchang... 1        200: 1                    7.09           
POST     /consumerApp/v2/confirm        216      200: 216                  849.40         
POST     /external/service-r/resolve... 12       200: 12                   9.73           
POST     /platform/combine-entry/inapp  22       200: 22                   42.35          
POST     /platform/entry                245      200: 245                  21.47          
----------------------------------------------------------------------------------------------------

總計: 13 個 API endpoints，1618 個請求

============================================================

錯誤摘要:
總處理行數: 4256
總錯誤數: 2638
錯誤率: 61.98%

錯誤類型分佈:
  regex_mismatch: 2638 (100.0%)

分析完成！成功處理了 13 個不同的 API endpoints。
```

## 多檔案處理範例

### 範例 2: 分析多個日誌檔案

```bash
python analyze_logs.py log1.txt log2.txt log3.txt
```

**輸出範例**:
```
開始分析 3 個日誌檔案:
  1. log1.txt
  2. log2.txt
  3. log3.txt
正在處理...

處理檔案 1/3: log1.txt
檔案 log1.txt: 處理 1000 行，找到 450 個有效請求
累計進度: 1000 行，450 個有效請求

處理檔案 2/3: log2.txt
檔案 log2.txt: 處理 1500 行，找到 680 個有效請求
累計進度: 2500 行，1130 個有效請求

處理檔案 3/3: log3.txt
檔案 log3.txt: 處理 800 行，找到 320 個有效請求
累計進度: 3300 行，1450 個有效請求

處理摘要:
  總檔案數: 3
  成功處理: 3
  處理失敗: 0

所有檔案處理完成！總共處理 3300 行，找到 1450 個有效請求。

============================================================
JKoPay API 日誌分析結果
==================================================

處理檔案數: 3

分析時間範圍: 2025-08-07 09:30:15 至 2025-08-07 12:45:20 (持續時間: 3 小時 15 分)

API Endpoint 統計資料:
[統計來自所有3個檔案的合併結果...]
```

### 範例 3: 使用萬用字元處理多個檔案

```bash
python analyze_logs.py *.txt
```

```bash
python analyze_logs.py logs/jkopay-*.txt
```

### 範例 4: 部分檔案處理失敗的情況

```bash
python analyze_logs.py existing_file.txt missing_file.txt another_file.txt
```

**輸出範例**:
```
開始分析 3 個日誌檔案:
  1. existing_file.txt
  2. missing_file.txt
  3. another_file.txt
正在處理...

處理檔案 1/3: existing_file.txt
檔案 existing_file.txt: 處理 2000 行，找到 800 個有效請求
累計進度: 2000 行，800 個有效請求

處理檔案 2/3: missing_file.txt
檔案 missing_file.txt 處理失敗: 無法存取檔案 'missing_file.txt'

處理檔案 3/3: another_file.txt
檔案 another_file.txt: 處理 1200 行，找到 500 個有效請求
累計進度: 3200 行，1300 個有效請求

處理摘要:
  總檔案數: 3
  成功處理: 2
  處理失敗: 1
  失敗檔案:
    missing_file.txt: 無法存取檔案 'missing_file.txt'

所有檔案處理完成！總共處理 3200 行，找到 1300 個有效請求。
[分析結果顯示成功處理的2個檔案的統計...]
```

## 時間範圍過濾範例

### 範例 5: 指定完整時間範圍

```bash
python analyze_logs.py jkopay-api-onlinepay.txt --start-time "2025-08-07 10:50:00" --end-time "2025-08-07 10:55:00"
```

**輸出範例**:
```
時間範圍過濾: 從 2025-08-07 10:50:00 至 2025-08-07 10:55:00
開始分析 1 個日誌檔案:
  1. jkopay-api-onlinepay.txt

[處理過程...]

所有檔案處理完成！總共處理 4256 行，找到 325 個有效請求。
時間範圍過濾結果:
  總處理請求數: 1618
  過濾排除數: 1293
  包含在範圍內: 325
  包含率: 20.1%

============================================================
JKoPay API 日誌分析結果
==================================================

分析時間範圍: 2025-08-07 10:50:00 至 2025-08-07 10:55:00 (持續時間: 5 分)
[只顯示指定時間範圍內的統計結果...]
```

### 範例 6: 只指定開始時間

```bash
python analyze_logs.py *.txt --start-time "2025-08-07 11:00:00"
```

### 範例 7: 只指定結束時間

```bash
python analyze_logs.py *.txt --end-time "2025-08-07 10:30:00"
```

### 範例 8: 多檔案配合時間過濾

```bash
python analyze_logs.py log1.txt log2.txt log3.txt --start-time "2025-08-07 10:00:00" --end-time "2025-08-07 11:00:00"
```

**輸出範例**:
```
時間範圍過濾: 從 2025-08-07 10:00:00 至 2025-08-07 11:00:00
開始分析 3 個日誌檔案:
  1. log1.txt
  2. log2.txt
  3. log3.txt

[處理所有檔案...]

所有檔案處理完成！總共處理 5000 行，找到 800 個有效請求。
時間範圍過濾結果:
  總處理請求數: 2400
  過濾排除數: 1600
  包含在範圍內: 800
  包含率: 33.3%

============================================================
JKoPay API 日誌分析結果
==================================================

處理檔案數: 3

分析時間範圍: 2025-08-07 10:00:00 至 2025-08-07 11:00:00 (持續時間: 1 小時)
[顯示3個檔案在指定時間範圍內的合併統計...]
```

### 範例 9: 支援的時間格式範例

```bash
# 完整日期時間格式
python analyze_logs.py logs.txt --start-time "2025-08-07 14:30:00"

# ISO 8601 格式
python analyze_logs.py logs.txt --start-time "2025-08-07T14:30:00"

# 含微秒格式
python analyze_logs.py logs.txt --start-time "2025-08-07T14:30:00.123456"

# 含時區格式
python analyze_logs.py logs.txt --start-time "2025-08-07T14:30:00+08:00"

# 只有日期（表示當天00:00:00）
python analyze_logs.py logs.txt --start-time "2025-08-07"

# 省略秒數
python analyze_logs.py logs.txt --start-time "2025-08-07 14:30"
```

## 錯誤情況處理範例

### 範例 10: 檔案不存在

```bash
python analyze_logs.py nonexistent_file.txt
```

**輸出**:
```
錯誤: 無法存取檔案 'nonexistent_file.txt'

錯誤摘要:
總處理行數: 0
總錯誤數: 1
錯誤率: 0.00%

錯誤類型分佈:
  file_not_found: 1 (100.0%)
```

### 範例 11: 時間範圍格式錯誤

```bash
python analyze_logs.py logs.txt --start-time "invalid-time-format"
```

**輸出**:
```
錯誤: 無法解析時間格式: invalid-time-format
```

### 範例 12: 開始時間晚於結束時間

```bash
python analyze_logs.py logs.txt --start-time "2025-08-07 12:00:00" --end-time "2025-08-07 10:00:00"
```

**輸出**:
```
錯誤: 開始時間必須早於結束時間
```

### 範例 13: 空檔案處理

```bash
python analyze_logs.py empty_file.txt
```

**輸出**:
```
開始分析日誌檔案: empty_file.txt
正在處理...
處理完成！總共處理 0 行，找到 0 個有效請求。

沒有找到有效的 HTTP 請求資料。

處理過程中沒有發生錯誤。

分析完成！成功處理了 0 個不同的 API endpoints。
```

### 範例 14: 混合格式日誌檔案

假設有一個包含混合格式的日誌檔案 `mixed_format.txt`:

```json
{"Timestamp":"2025-08-07T10:49:40.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \"GET\" \"/api/users\" responded 200 in 10.5 ms"}
Plain text log line
{"Timestamp":"2025-08-07T10:49:42.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \"POST\" \"/api/orders\" responded 201 in 25.3 ms"}
[ERROR] Some error message
{"Timestamp":"2025-08-07T10:49:44.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \"DELETE\" \"/api/users/123\" responded 204 in 8.1 ms"}
```

```bash
python analyze_logs.py mixed_format.txt
```

**輸出**:
```
開始分析日誌檔案: mixed_format.txt
正在處理...
處理完成！總共處理 5 行，找到 3 個有效請求。

============================================================
JKoPay API 日誌分析結果
==================================================

分析時間範圍: 2025-08-07 10:49:40 至 2025-08-07 10:49:44 (持續時間: 4 秒)

API Endpoint 統計資料:
----------------------------------------------------------------------------------------------------
方法       路徑                             請求數      狀態碼分佈                     平均回應時間(ms)     
----------------------------------------------------------------------------------------------------
DELETE   /api/users/123                 1        204: 1                    8.10           
GET      /api/users                     1        200: 1                    10.50          
POST     /api/orders                    1        201: 1                    25.30          
----------------------------------------------------------------------------------------------------

總計: 3 個 API endpoints，3 個請求

============================================================

錯誤摘要:
總處理行數: 5
總錯誤數: 2
錯誤率: 40.00%

錯誤類型分佈:
  json_parse_error: 2 (100.0%)

分析完成！成功處理了 3 個不同的 API endpoints。
```

## 效能分析範例

### 範例 5: 大檔案處理效能

對於大型日誌檔案，工具會顯示處理進度：

```bash
python analyze_logs.py large_log_file.txt
```

**輸出**:
```
開始分析日誌檔案: large_log_file.txt
正在處理...
已處理 1000 行，找到 450 個有效請求...
已處理 2000 行，找到 890 個有效請求...
已處理 3000 行，找到 1340 個有效請求...
...
已處理 50000 行，找到 22500 個有效請求...
處理完成！總共處理 52341 行，找到 23156 個有效請求。

[分析結果...]

分析完成！成功處理了 25 個不同的 API endpoints。
```

## 分析結果解讀

### 理解輸出資料

1. **時間範圍**: 顯示日誌檔案涵蓋的時間區間
2. **API Endpoint 統計**:
   - **方法**: HTTP 方法（GET, POST, PUT, DELETE 等）
   - **路徑**: API 端點路徑（長路徑會被截斷並顯示 "..."）
   - **請求數**: 該端點的總請求次數
   - **狀態碼分佈**: 各種 HTTP 狀態碼的出現次數
   - **平均回應時間**: 該端點的平均回應時間（毫秒）

3. **錯誤摘要**:
   - **總處理行數**: 檔案中的總行數
   - **總錯誤數**: 無法處理的行數
   - **錯誤率**: 錯誤行數佔總行數的百分比
   - **錯誤類型分佈**: 各種錯誤類型的統計

### 常見錯誤類型說明

- `json_parse_error`: JSON 格式錯誤
- `regex_mismatch`: HTTP 訊息格式不匹配
- `empty_line`: 空行
- `field_extraction_error`: 欄位提取失敗
- `timestamp_parse_error`: 時間戳記解析失敗
- `file_not_found`: 檔案不存在
- `permission_denied`: 權限不足

## 進階使用技巧

### 技巧 1: 時間範圍分析策略

使用內建的時間範圍過濾功能進行精確分析：

```bash
# 分析高峰時段（上午10-11點）
python analyze_logs.py *.txt --start-time "2025-08-07 10:00:00" --end-time "2025-08-07 11:00:00"

# 分析特定事件前後的時間段
python analyze_logs.py incident_logs.txt --start-time "2025-08-07 14:55:00" --end-time "2025-08-07 15:05:00"

# 比較不同時間段的效能
python analyze_logs.py *.txt --start-time "2025-08-07 09:00:00" --end-time "2025-08-07 12:00:00"
python analyze_logs.py *.txt --start-time "2025-08-07 13:00:00" --end-time "2025-08-07 16:00:00"
```

### 技巧 2: 多檔案分析策略

```bash
# 按時間順序分析多個檔案
python analyze_logs.py morning_logs.txt afternoon_logs.txt evening_logs.txt

# 分析特定服務的所有日誌
python analyze_logs.py payment_service_*.txt

# 跨日期分析趨勢
python analyze_logs.py 2025-08-06_*.txt 2025-08-07_*.txt 2025-08-08_*.txt
```

### 技巧 3: 分析特定 API 端點

```bash
# 使用 grep 預過濾特定端點
grep "/platform/inquiry" jkopay-api-onlinepay.txt > inquiry_logs.txt
python analyze_logs.py inquiry_logs.txt

# 結合時間範圍分析特定端點的效能變化
python analyze_logs.py inquiry_logs.txt --start-time "2025-08-07 10:00:00" --end-time "2025-08-07 11:00:00"
```

### 技巧 4: 效能監控

使用工具來監控 API 效能趨勢：

1. 定期執行分析
2. 比較不同時間段的平均回應時間
3. 識別效能異常的端點

### 技巧 5: 錯誤率分析

- 正常錯誤率通常在 50-70% 之間（因為日誌包含非 HTTP 訊息）
- 如果錯誤率突然增加，可能表示日誌格式發生變化
- 檢查錯誤類型分佈來診斷問題

## 自動化腳本範例

### 範例腳本: 使用多檔案功能批次分析

```bash
#!/bin/bash
# batch_analyze_multi.sh

echo "開始批次分析所有日誌檔案..."
python analyze_logs.py *.txt > "combined_analysis_$(date +%Y%m%d_%H%M%S).txt"
echo "所有檔案的合併分析結果已儲存"
```

### 範例腳本: 時間範圍分析

```bash
#!/bin/bash
# time_range_analysis.sh

DATE="2025-08-07"
START_TIME="$DATE 09:00:00"
END_TIME="$DATE 18:00:00"

echo "分析工作時間 ($START_TIME 至 $END_TIME) 的日誌..."
python analyze_logs.py *.txt --start-time "$START_TIME" --end-time "$END_TIME" > "business_hours_analysis_$(date +%Y%m%d).txt"

echo "分析非工作時間的日誌..."
python analyze_logs.py *.txt --end-time "$START_TIME" > "before_business_analysis_$(date +%Y%m%d).txt"
python analyze_logs.py *.txt --start-time "$END_TIME" > "after_business_analysis_$(date +%Y%m%d).txt"
```

### 範例腳本: 批次處理個別檔案（傳統方法）

```bash
#!/bin/bash
# batch_analyze_individual.sh

for log_file in *.txt; do
    echo "分析檔案: $log_file"
    python analyze_logs.py "$log_file" > "${log_file%.txt}_analysis.txt"
    echo "結果已儲存到: ${log_file%.txt}_analysis.txt"
    echo "---"
done
```

### 範例腳本: 定期分析並儲存結果

```bash
#!/bin/bash
# daily_analysis.sh

DATE=$(date +%Y%m%d)
LOG_FILE="jkopay-api-onlinepay-$DATE.txt"
RESULT_FILE="analysis_$DATE.txt"

if [ -f "$LOG_FILE" ]; then
    python analyze_logs.py "$LOG_FILE" > "$RESULT_FILE"
    echo "日誌分析完成，結果儲存到: $RESULT_FILE"
else
    echo "日誌檔案不存在: $LOG_FILE"
fi
```

## 疑難排解

### 問題: 多檔案處理速度慢

**可能原因**:
- 檔案數量過多
- 個別檔案過大
- 磁碟 I/O 瓶頸

**解決方案**:
- 使用時間範圍過濾減少處理的資料量
- 批次處理，分組執行
- 確保有足夠的可用記憶體
- 使用 SSD 儲存裝置

### 問題: 時間範圍過濾結果不符預期

**可能原因**:
- 日誌檔案的時間戳記格式與預期不同
- 時間範圍設定錯誤
- 日誌中的時間戳記有時區問題

**解決方案**:
- 檢查日誌檔案中實際的時間戳記格式
- 使用不同的時間格式進行嘗試
- 注意時區設定，確保時間範圍正確

### 問題: 結果不準確

**檢查項目**:
- 確認日誌格式符合預期
- 檢查時間戳記格式
- 驗證 HTTP 訊息格式

### 問題: 記憶體使用過高

**解決方案**:
- 工具已使用串流處理，記憶體使用應該穩定
- 如果仍有問題，檢查系統其他程式的記憶體使用
- 考慮重啟系統清理記憶體

## 新功能總結

### v1.1.0 新增功能

1. **多檔案處理**:
   - 支援同時處理多個日誌檔案
   - 自動合併所有檔案的統計結果
   - 單一檔案失敗不影響其他檔案處理
   - 提供詳細的檔案處理狀態報告

2. **時間範圍過濾**:
   - 支援指定開始時間和結束時間
   - 支援多種時間格式
   - 提供過濾統計和包含率資訊
   - 可與多檔案處理結合使用

3. **增強的錯誤處理**:
   - 更好的錯誤隔離機制
   - 詳細的錯誤類型報告
   - 部分失敗時的優雅降級

這些功能大幅提升了工具的實用性和靈活性，讓您能夠更有效地分析大量的日誌資料。

---

這些範例應該能幫助您有效使用 JKoPay API 日誌分析工具的所有功能。如有其他問題，請參考 README.md 或聯絡開發團隊。
