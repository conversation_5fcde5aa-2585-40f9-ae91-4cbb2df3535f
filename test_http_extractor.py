"""
HTTPInfoExtractor 類別的單元測試

測試 HTTP 資訊提取功能，包括標準解析、邊界情況和錯誤處理。
"""

import unittest
from analyze_logs import HTTPInfoExtractor, HTTPRequestInfo


class TestHTTPInfoExtractor(unittest.TestCase):
    """HTTPInfoExtractor 類別的測試案例"""

    def setUp(self):
        """設置測試環境"""
        self.extractor = HTTPInfoExtractor()

    def test_extract_standard_http_messages(self):
        """測試標準 HTTP 訊息解析"""
        # 測試各種標準的 HTTP 訊息格式
        test_cases = [
            {
                "message": 'HTTP "GET" "/api/test" responded 200 in 45.67 ms',
                "expected": HTTPRequestInfo("GET", "/api/test", 200, 45.67)
            },
            {
                "message": 'HTTP "POST" "/api/payment" responded 201 in 123.45 ms',
                "expected": HTTPRequestInfo("POST", "/api/payment", 201, 123.45)
            },
            {
                "message": 'HTTP "PUT" "/api/user/123" responded 200 in 89.12 ms',
                "expected": HTTPRequestInfo("PUT", "/api/user/123", 200, 89.12)
            },
            {
                "message": 'HTTP "DELETE" "/api/resource/456" responded 204 in 12.34 ms',
                "expected": HTTPRequestInfo("DELETE", "/api/resource/456", 204, 12.34)
            },
            {
                "message": 'HTTP "PATCH" "/api/update" responded 200 in 67.89 ms',
                "expected": HTTPRequestInfo("PATCH", "/api/update", 200, 67.89)
            }
        ]

        for test_case in test_cases:
            with self.subTest(message=test_case["message"]):
                result = self.extractor.extract_http_info(test_case["message"])

                self.assertIsNotNone(result)
                self.assertIsInstance(result, HTTPRequestInfo)
                self.assertEqual(result.method, test_case["expected"].method)
                self.assertEqual(result.path, test_case["expected"].path)
                self.assertEqual(result.status_code, test_case["expected"].status_code)
                self.assertAlmostEqual(result.elapsed_time, test_case["expected"].elapsed_time, places=2)

    def test_extract_various_status_codes(self):
        """測試各種狀態碼的解析"""
        status_codes = [200, 201, 204, 301, 302, 400, 401, 403, 404, 500, 502, 503]

        for status_code in status_codes:
            message = f'HTTP "GET" "/api/test" responded {status_code} in 50.0 ms'
            with self.subTest(status_code=status_code):
                result = self.extractor.extract_http_info(message)

                self.assertIsNotNone(result)
                self.assertEqual(result.status_code, status_code)

    def test_extract_various_elapsed_times(self):
        """測試各種回應時間格式的解析"""
        test_cases = [
            ("1.0", 1.0),
            ("12.34", 12.34),
            ("123.456", 123.456),
            ("1000.0", 1000.0),
            ("0.1", 0.1),
            ("999.999", 999.999)
        ]

        for time_str, expected_time in test_cases:
            message = f'HTTP "GET" "/api/test" responded 200 in {time_str} ms'
            with self.subTest(elapsed_time=time_str):
                result = self.extractor.extract_http_info(message)

                self.assertIsNotNone(result)
                self.assertAlmostEqual(result.elapsed_time, expected_time, places=3)

    def test_extract_complex_paths(self):
        """測試複雜路徑的解析"""
        test_cases = [
            "/api/v1/users/123/profile",
            "/payment/jkopay/callback",
            "/api/transactions?status=pending&limit=10",
            "/users/profile/settings",
            "/api/v2/payments/webhook",
            "/health-check",
            "/",
            "/api/users/123/orders/456/items/789"
        ]

        for path in test_cases:
            message = f'HTTP "GET" "{path}" responded 200 in 50.0 ms'
            with self.subTest(path=path):
                result = self.extractor.extract_http_info(message)

                self.assertIsNotNone(result)
                self.assertEqual(result.path, path)

    def test_regex_no_match_cases(self):
        """測試正規表示式不匹配的情況"""
        # 測試各種不符合格式的訊息
        invalid_messages = [
            "This is not an HTTP message",
            'HTTP GET /api/test responded 200 in 50.0 ms',  # 缺少引號
            'HTTP "GET" /api/test responded 200 in 50.0 ms',  # 路徑缺少引號
            'HTTP "GET" "/api/test" returned 200 in 50.0 ms',  # 使用 returned 而非 responded
            'HTTP "GET" "/api/test" responded 200 after 50.0 ms',  # 使用 after 而非 in
            'HTTP "GET" "/api/test" responded 200 in 50.0 seconds',  # 使用 seconds 而非 ms
            'Request: GET /api/test Status: 200 Time: 50.0ms',  # 完全不同的格式
            '',  # 空字串
            'HTTP "GET" "/api/test" responded in 50.0 ms',  # 缺少狀態碼
            'HTTP "/api/test" responded 200 in 50.0 ms',  # 缺少方法
        ]

        for invalid_message in invalid_messages:
            with self.subTest(message=invalid_message):
                result = self.extractor.extract_http_info(invalid_message)
                self.assertIsNone(result)

    def test_invalid_input_types(self):
        """測試無效輸入型別的處理"""
        invalid_inputs = [
            None,
            123,
            [],
            {},
            set(),
            True,
            False
        ]

        for invalid_input in invalid_inputs:
            with self.subTest(input_data=invalid_input):
                result = self.extractor.extract_http_info(invalid_input)
                self.assertIsNone(result)

    def test_invalid_numeric_values(self):
        """測試無效數值的處理"""
        # 測試狀態碼無法轉換為整數的情況
        invalid_status_messages = [
            'HTTP "GET" "/api/test" responded abc in 50.0 ms',
            'HTTP "GET" "/api/test" responded 200.5 in 50.0 ms',
            'HTTP "GET" "/api/test" responded "" in 50.0 ms',
        ]

        for message in invalid_status_messages:
            with self.subTest(message=message):
                result = self.extractor.extract_http_info(message)
                self.assertIsNone(result)

        # 測試回應時間無法轉換為浮點數的情況
        invalid_time_messages = [
            'HTTP "GET" "/api/test" responded 200 in abc ms',
            'HTTP "GET" "/api/test" responded 200 in "" ms',
            'HTTP "GET" "/api/test" responded 200 in 50.0.0 ms',
        ]

        for message in invalid_time_messages:
            with self.subTest(message=message):
                result = self.extractor.extract_http_info(message)
                self.assertIsNone(result)

    def test_edge_case_whitespace(self):
        """測試邊界情況：空白字元處理"""
        # 測試包含額外空白的訊息
        message_with_spaces = '  HTTP "GET" "/api/test" responded 200 in 50.0 ms  '
        result = self.extractor.extract_http_info(message_with_spaces)

        self.assertIsNotNone(result)
        self.assertEqual(result.method, "GET")
        self.assertEqual(result.path, "/api/test")
        self.assertEqual(result.status_code, 200)
        self.assertAlmostEqual(result.elapsed_time, 50.0, places=1)

    def test_regex_pattern_compilation(self):
        """測試正規表示式預編譯"""
        # 驗證 extractor 使用預編譯的正規表示式
        self.assertIsNotNone(self.extractor.http_pattern)

        # 測試正規表示式物件的基本功能
        test_message = 'HTTP "GET" "/api/test" responded 200 in 50.0 ms'
        match = self.extractor.http_pattern.search(test_message)

        self.assertIsNotNone(match)
        self.assertEqual(match.group(1), "GET")
        self.assertEqual(match.group(2), "/api/test")
        self.assertEqual(match.group(3), "200")
        self.assertEqual(match.group(4), "50.0")

    def test_integration_with_json_parser_output(self):
        """測試與 JSONLogParser 輸出的整合"""
        # 模擬從 JSONLogParser 獲得的 RenderedMessage
        rendered_messages = [
            'HTTP "GET" "/api/users" responded 200 in 45.67 ms',
            'HTTP "POST" "/api/payments" responded 201 in 123.45 ms',
            'HTTP "PUT" "/api/users/123" responded 200 in 89.12 ms',
            'Some other log message that should be ignored',
            'HTTP "DELETE" "/api/sessions/456" responded 204 in 12.34 ms'
        ]

        expected_results = [
            HTTPRequestInfo("GET", "/api/users", 200, 45.67),
            HTTPRequestInfo("POST", "/api/payments", 201, 123.45),
            HTTPRequestInfo("PUT", "/api/users/123", 200, 89.12),
            None,  # 應該被忽略
            HTTPRequestInfo("DELETE", "/api/sessions/456", 204, 12.34)
        ]

        for i, message in enumerate(rendered_messages):
            with self.subTest(message=message):
                result = self.extractor.extract_http_info(message)
                expected = expected_results[i]

                if expected is None:
                    self.assertIsNone(result)
                else:
                    self.assertIsNotNone(result)
                    self.assertEqual(result.method, expected.method)
                    self.assertEqual(result.path, expected.path)
                    self.assertEqual(result.status_code, expected.status_code)
                    self.assertAlmostEqual(result.elapsed_time, expected.elapsed_time, places=2)


if __name__ == '__main__':
    unittest.main()
