時間範圍過濾: 從 2025-08-07 16:50:00 至 2025-08-07 17:00:00
開始分析 4 個日誌檔案:
  1. 1.txt
  2. 2.txt
  3. 3.txt
  4. 4.txt
正在處理...

處理檔案 1/4: 1.txt
檔案 1.txt: 處理 4087 行，找到 1504 個有效請求
累計進度: 4087 行，1504 個有效請求

處理檔案 2/4: 2.txt
檔案 2.txt: 處理 4757 行，找到 1497 個有效請求
累計進度: 8844 行，3001 個有效請求

處理檔案 3/4: 3.txt
檔案 3.txt: 處理 5128 行，找到 1505 個有效請求
累計進度: 13972 行，4506 個有效請求

處理檔案 4/4: 4.txt
檔案 4.txt: 處理 5366 行，找到 1510 個有效請求
累計進度: 19338 行，6016 個有效請求

處理摘要:
  總檔案數: 4
  成功處理: 4
  處理失敗: 0

所有檔案處理完成！總共處理 19338 行，找到 6016 個有效請求。
時間範圍過濾結果:
  總處理請求數: 8335
  過濾排除數: 2319
  包含在範圍內: 6016
  包含率: 72.2%

============================================================
JKoPay API 日誌分析結果
==================================================

處理檔案數: 4

分析時間範圍: 2025-08-07 16:50:00 至 2025-08-07 16:59:59 (持續時間: 9 分 59 秒)

API Endpoint 統計資料:
----------------------------------------------------------------------------------------------------
方法       路徑                             請求數      狀態碼分佈                     平均回應時間(ms)     
----------------------------------------------------------------------------------------------------
GET      /external/qr                   24       200: 24                   64.15          
GET      /external/service-r/knock      188      200: 188                  9.98           
GET      /platform/inquiry              3080     200: 3080                 12.76          
GET      /platform_reimburse/reimbur... 27       200: 27                   6.41           
GET      /platform_reimburse/store/b... 27       200: 27                   38.92          
GET      /web/paymentRouter             838      302: 838                  2.03           
OPTIONS  /external/service-r/resolve... 16       204: 16                   0.05           
POST     /consumerApp/v2/confirm        821      200: 821                  987.86         
POST     /external/service-r/resolve... 16       200: 16                   10.68          
POST     /platform/combine-entry/inapp  68       200: 68                   70.90          
POST     /platform/entry                911      200: 911                  21.54          
----------------------------------------------------------------------------------------------------

總計: 11 個 API endpoints，6016 個請求

============================================================

錯誤摘要:
總處理行數: 19338
總錯誤數: 11003
錯誤率: 56.90%

錯誤類型分佈:
  regex_mismatch: 11003 (100.0%)

分析完成！成功處理了 11 個不同的 API endpoints。
