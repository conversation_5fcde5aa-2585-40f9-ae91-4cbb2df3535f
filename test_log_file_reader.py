"""
LogFileReader 類別的單元測試

測試檔案讀取功能，包括正常讀取、檔案不存在和權限錯誤的處理。
"""

import os
import tempfile
import unittest
from unittest.mock import patch, mock_open
from analyze_logs import LogFileReader


class TestLogFileReader(unittest.TestCase):
    """LogFileReader 類別的測試案例"""

    def setUp(self):
        """測試前的設定"""
        # 建立臨時檔案用於測試
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8')
        self.temp_file_path = self.temp_file.name

        # 寫入測試資料
        test_data = [
            '{"Timestamp": "2023-01-01T12:00:00Z", "RenderedMessage": "HTTP \\"GET\\" \\"/api/test\\" responded 200 in 150.5 ms"}\n',
            '{"Timestamp": "2023-01-01T12:01:00Z", "RenderedMessage": "HTTP \\"POST\\" \\"/api/payment\\" responded 201 in 250.0 ms"}\n',
            '{"Timestamp": "2023-01-01T12:02:00Z", "RenderedMessage": "HTTP \\"GET\\" \\"/api/status\\" responded 404 in 50.2 ms"}\n'
        ]

        for line in test_data:
            self.temp_file.write(line)
        self.temp_file.close()

    def tearDown(self):
        """測試後的清理"""
        # 刪除臨時檔案
        if os.path.exists(self.temp_file_path):
            os.unlink(self.temp_file_path)

    def test_is_file_accessible_valid_file(self):
        """測試檢查有效檔案的存取權限"""
        reader = LogFileReader(self.temp_file_path)
        self.assertTrue(reader.is_file_accessible())

    def test_is_file_accessible_nonexistent_file(self):
        """測試檢查不存在檔案的存取權限"""
        reader = LogFileReader("nonexistent_file.txt")
        self.assertFalse(reader.is_file_accessible())

    def test_is_file_accessible_directory(self):
        """測試檢查目錄而非檔案的情況"""
        # 使用臨時目錄進行測試
        with tempfile.TemporaryDirectory() as temp_dir:
            reader = LogFileReader(temp_dir)
            self.assertFalse(reader.is_file_accessible())

    @patch('os.access')
    def test_is_file_accessible_no_permission(self, mock_access):
        """測試檢查沒有讀取權限的檔案"""
        # 模擬沒有讀取權限的情況
        mock_access.return_value = False

        reader = LogFileReader(self.temp_file_path)
        self.assertFalse(reader.is_file_accessible())

    def test_read_lines_valid_file(self):
        """測試正常檔案讀取"""
        reader = LogFileReader(self.temp_file_path)
        lines = list(reader.read_lines())

        # 驗證讀取的行數
        self.assertEqual(len(lines), 3)

        # 驗證第一行內容
        self.assertIn("GET", lines[0])
        self.assertIn("/api/test", lines[0])

        # 驗證第二行內容
        self.assertIn("POST", lines[1])
        self.assertIn("/api/payment", lines[1])

        # 驗證第三行內容
        self.assertIn("GET", lines[2])
        self.assertIn("/api/status", lines[2])

    def test_read_lines_nonexistent_file(self):
        """測試讀取不存在的檔案"""
        reader = LogFileReader("nonexistent_file.txt")

        with self.assertRaises(FileNotFoundError) as context:
            list(reader.read_lines())

        self.assertIn("檔案不存在", str(context.exception))

    def test_read_lines_directory_instead_of_file(self):
        """測試讀取目錄而非檔案的情況"""
        with tempfile.TemporaryDirectory() as temp_dir:
            reader = LogFileReader(temp_dir)

            with self.assertRaises(IOError) as context:
                list(reader.read_lines())

            self.assertIn("不是檔案", str(context.exception))

    @patch('os.access')
    def test_read_lines_no_permission(self, mock_access):
        """測試讀取沒有權限的檔案"""
        # 模擬沒有讀取權限的情況
        mock_access.return_value = False

        reader = LogFileReader(self.temp_file_path)

        with self.assertRaises(PermissionError) as context:
            list(reader.read_lines())

        self.assertIn("沒有讀取權限", str(context.exception))

    def test_read_lines_empty_file(self):
        """測試讀取空檔案"""
        # 建立空檔案
        empty_file = tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8')
        empty_file_path = empty_file.name
        empty_file.close()

        try:
            reader = LogFileReader(empty_file_path)
            lines = list(reader.read_lines())

            # 空檔案應該返回空列表
            self.assertEqual(len(lines), 0)

        finally:
            # 清理空檔案
            if os.path.exists(empty_file_path):
                os.unlink(empty_file_path)

    def test_read_lines_iterator_behavior(self):
        """測試迭代器行為"""
        reader = LogFileReader(self.temp_file_path)
        line_iterator = reader.read_lines()

        # 驗證返回的是迭代器
        self.assertTrue(hasattr(line_iterator, '__iter__'))
        self.assertTrue(hasattr(line_iterator, '__next__'))

        # 逐一讀取行
        first_line = next(line_iterator)
        self.assertIn("GET", first_line)

        second_line = next(line_iterator)
        self.assertIn("POST", second_line)

        third_line = next(line_iterator)
        self.assertIn("GET", third_line)

        # 應該沒有更多行了
        with self.assertRaises(StopIteration):
            next(line_iterator)

    @patch('builtins.open')
    def test_read_lines_io_error(self, mock_open_func):
        """測試讀取檔案時發生 I/O 錯誤"""
        # 模擬 I/O 錯誤
        mock_open_func.side_effect = IOError("模擬的 I/O 錯誤")

        reader = LogFileReader(self.temp_file_path)

        with self.assertRaises(IOError) as context:
            list(reader.read_lines())

        self.assertIn("讀取檔案時發生錯誤", str(context.exception))

    def test_read_lines_with_unicode_content(self):
        """測試讀取包含 Unicode 字元的檔案"""
        # 建立包含中文字元的測試檔案
        unicode_file = tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8')
        unicode_file_path = unicode_file.name

        unicode_content = [
            '{"Timestamp": "2023-01-01T12:00:00Z", "RenderedMessage": "測試中文內容"}\n',
            '{"Timestamp": "2023-01-01T12:01:00Z", "RenderedMessage": "HTTP \\"GET\\" \\"/api/測試\\" responded 200 in 100.0 ms"}\n'
        ]

        for line in unicode_content:
            unicode_file.write(line)
        unicode_file.close()

        try:
            reader = LogFileReader(unicode_file_path)
            lines = list(reader.read_lines())

            # 驗證能正確讀取中文內容
            self.assertEqual(len(lines), 2)
            self.assertIn("測試中文內容", lines[0])
            self.assertIn("/api/測試", lines[1])

        finally:
            # 清理測試檔案
            if os.path.exists(unicode_file_path):
                os.unlink(unicode_file_path)


if __name__ == '__main__':
    unittest.main()
