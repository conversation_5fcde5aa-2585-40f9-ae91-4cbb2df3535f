# Requirements Document

## Introduction

這個功能是一個 Python 日誌分析工具，專門用於分析 JKoPay API 的線上支付日誌檔案。工具會解析日誌中的 HTTP 請求資訊，包括請求方法、路徑、狀態碼和回應時間，並提供統計分析結果，幫助開發者了解 API 的使用情況和效能表現。

## Requirements

### Requirement 1

**User Story:** 作為一個開發者，我想要能夠讀取和解析日誌檔案，以便從中提取 HTTP 請求的相關資訊。

#### Acceptance Criteria

1. WHEN 系統讀取 `jkopay-api-onlinepay.txt` 檔案 THEN 系統 SHALL 逐行處理檔案內容以避免記憶體溢出
2. WHEN 系統遇到有效的 JSON 日誌行 THEN 系統 SHALL 解析 JSON 並提取 `RenderedMessage` 欄位
3. WHEN 系統處理 `RenderedMessage` 欄位 THEN 系統 SHALL 使用正規表示式 `HTTP "(\w+)" "([^"]+)" responded (\d{3}) in ([\d\.]+) ms` 提取 HTTP 資訊
4. WHEN 系統遇到無效的 JSON 或不符合格式的日誌行 THEN 系統 SHALL 跳過該行並繼續處理下一行
5. WHEN 檔案讀取完成 THEN 系統 SHALL 確保所有有效的日誌行都已被處理

### Requirement 2

**User Story:** 作為一個開發者，我想要系統能夠統計每個 API endpoint 的請求資訊，以便了解各個 API 的使用情況。

#### Acceptance Criteria

1. WHEN 系統解析到一個 HTTP 請求 THEN 系統 SHALL 以 (RequestMethod, RequestPath) 作為唯一識別鍵
2. WHEN 系統處理同一個 endpoint 的多個請求 THEN 系統 SHALL 累計該 endpoint 的總請求次數
3. WHEN 系統記錄狀態碼 THEN 系統 SHALL 統計每個 endpoint 各種狀態碼的出現次數
4. WHEN 系統記錄回應時間 THEN 系統 SHALL 儲存所有回應時間數值以供後續計算使用

### Requirement 3

**User Story:** 作為一個開發者，我想要看到每個 API endpoint 的效能統計，以便識別可能的效能問題。

#### Acceptance Criteria

1. WHEN 系統完成所有日誌處理 THEN 系統 SHALL 計算每個 endpoint 的平均回應時間
2. WHEN 計算平均回應時間 THEN 系統 SHALL 使用公式：總回應時間 / 總請求次數
3. WHEN 系統準備輸出結果 THEN 系統 SHALL 包含 endpoint 識別、總請求數、狀態碼分佈和平均回應時間
4. WHEN 系統顯示結果 THEN 系統 SHALL 以清晰的表格格式呈現所有統計資訊

### Requirement 4

**User Story:** 作為一個開發者，我想要工具具有良好的錯誤處理能力，以便在遇到問題時能夠穩定運行。

#### Acceptance Criteria

1. WHEN 指定的日誌檔案不存在 THEN 系統 SHALL 顯示清楚的錯誤訊息並優雅地退出
2. WHEN 日誌檔案無法讀取 THEN 系統 SHALL 顯示權限或其他相關錯誤訊息
3. WHEN 系統遇到記憶體不足的情況 THEN 系統 SHALL 透過逐行讀取來最小化記憶體使用
4. WHEN 系統處理過程中發生未預期錯誤 THEN 系統 SHALL 提供有意義的錯誤訊息而不是崩潰

### Requirement 5

**User Story:** 作為一個開發者，我想要了解日誌檔案的時間範圍，以便知道分析資料涵蓋的時間區間。

#### Acceptance Criteria

1. WHEN 系統解析 JSON 日誌行 THEN 系統 SHALL 提取 `Timestamp` 欄位中的 ISO 8601 格式時間戳記
2. WHEN 系統遇到第一個有效的時間戳記 THEN 系統 SHALL 記錄該時間作為日誌開始時間
3. WHEN 系統處理每一行有效日誌 THEN 系統 SHALL 持續更新最新的時間戳記作為日誌結束時間
4. WHEN 系統完成日誌分析 THEN 系統 SHALL 在結果中顯示格式化的日誌開始時間和結束時間
5. WHEN 日誌中沒有有效的時間戳記 THEN 系統 SHALL 顯示適當的訊息說明無法確定時間範圍

### Requirement 6

**User Story:** 作為一個開發者，我想要工具能夠處理大型日誌檔案，以便分析生產環境的完整日誌資料。

#### Acceptance Criteria

1. WHEN 處理大型檔案 THEN 系統 SHALL 使用串流讀取方式而非一次性載入全部內容
2. WHEN 系統處理日誌行 THEN 系統 SHALL 即時更新統計資料而非等待全部讀取完成
3. WHEN 系統運行時 THEN 系統 SHALL 保持合理的記憶體使用量不隨檔案大小線性增長
4. WHEN 處理超大檔案 THEN 系統 SHALL 在合理時間內完成分析並提供結果
