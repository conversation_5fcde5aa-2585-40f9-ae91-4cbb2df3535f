# Design Document

## Overview

日誌分析工具是一個 Python 命令列應用程式，專門設計來分析 JKoPay API 的 JSON 格式日誌檔案。系統採用串流處理架構，逐行讀取和解析日誌，提取 HTTP 請求資訊並進行統計分析，最終以表格形式呈現結果。

## Architecture

### 系統架構圖

```mermaid
graph TD
    A[日誌檔案] --> B[檔案讀取器]
    B --> C[JSON 解析器]
    C --> D[HTTP 資訊提取器]
    D --> E[資料彙總器]
    E --> F[統計計算器]
    F --> G[結果格式化器]
    G --> H[終端輸出]
    
    I[時間戳記追蹤器] --> E
    J[錯誤處理器] --> C
    J --> D
```

### 核心設計原則

1. **記憶體效率** - 使用串流處理，避免一次性載入整個檔案
2. **錯誤容忍** - 跳過無效的日誌行，繼續處理其他資料
3. **可擴展性** - 模組化設計，便於未來功能擴展
4. **效能優化** - 使用正規表示式預編譯和高效的資料結構

## Components and Interfaces

### 1. LogFileReader 類別

**職責**: 負責檔案讀取和基本錯誤處理

```python
class LogFileReader:
    def __init__(self, file_path: str)
    def read_lines(self) -> Iterator[str]
    def is_file_accessible(self) -> bool
```

**介面說明**:
- `read_lines()`: 返回一個迭代器，逐行讀取檔案內容
- `is_file_accessible()`: 檢查檔案是否存在且可讀取

### 2. JSONLogParser 類別

**職責**: 解析 JSON 格式的日誌行並提取相關欄位

```python
class JSONLogParser:
    def parse_log_line(self, line: str) -> Optional[Dict[str, Any]]
    def extract_rendered_message(self, log_data: Dict) -> Optional[str]
    def extract_timestamp(self, log_data: Dict) -> Optional[datetime]
```

**介面說明**:
- `parse_log_line()`: 將 JSON 字串解析為字典物件
- `extract_rendered_message()`: 從解析後的 JSON 中提取 RenderedMessage 欄位
- `extract_timestamp()`: 從解析後的 JSON 中提取並轉換時間戳記

### 3. HTTPInfoExtractor 類別

**職責**: 從 RenderedMessage 中提取 HTTP 請求資訊

```python
class HTTPInfoExtractor:
    def __init__(self)
    def extract_http_info(self, rendered_message: str) -> Optional[HTTPRequestInfo]
```

**資料模型**:
```python
@dataclass
class HTTPRequestInfo:
    method: str
    path: str
    status_code: int
    elapsed_time: float
```

### 4. DataAggregator 類別

**職責**: 彙總和統計 HTTP 請求資料

```python
class DataAggregator:
    def __init__(self)
    def add_request(self, request_info: HTTPRequestInfo, timestamp: datetime)
    def get_aggregated_data(self) -> Dict[Tuple[str, str], EndpointStats]
    def get_time_range(self) -> Tuple[Optional[datetime], Optional[datetime]]
```

**資料模型**:
```python
@dataclass
class EndpointStats:
    count: int
    status_codes: Dict[int, int]
    elapsed_times: List[float]
    average_elapsed: float = 0.0
```

### 5. StatisticsCalculator 類別

**職責**: 計算統計資料，如平均回應時間

```python
class StatisticsCalculator:
    @staticmethod
    def calculate_average_elapsed(elapsed_times: List[float]) -> float
    @staticmethod
    def format_status_code_distribution(status_codes: Dict[int, int]) -> str
```

### 6. ResultFormatter 類別

**職責**: 格式化輸出結果

```python
class ResultFormatter:
    def format_results(self, 
                      aggregated_data: Dict[Tuple[str, str], EndpointStats],
                      time_range: Tuple[Optional[datetime], Optional[datetime]]) -> str
    def format_time_range(self, start_time: datetime, end_time: datetime) -> str
```

## Data Models

### 核心資料結構

1. **HTTPRequestInfo**: 儲存單一 HTTP 請求的資訊
2. **EndpointStats**: 儲存每個 endpoint 的統計資料
3. **LogAnalysisResult**: 儲存完整的分析結果

### 資料流程

```mermaid
sequenceDiagram
    participant F as File
    participant R as Reader
    participant P as Parser
    participant E as Extractor
    participant A as Aggregator
    participant C as Calculator
    participant O as Output

    F->>R: 讀取日誌行
    R->>P: 傳遞原始日誌行
    P->>P: 解析 JSON
    P->>E: 傳遞 RenderedMessage
    E->>E: 提取 HTTP 資訊
    E->>A: 傳遞 HTTPRequestInfo
    A->>A: 彙總統計資料
    A->>C: 請求計算平均值
    C->>A: 返回計算結果
    A->>O: 傳遞最終結果
```

## Error Handling

### 錯誤處理策略

1. **檔案層級錯誤**
   - 檔案不存在: 顯示清楚的錯誤訊息並退出
   - 權限不足: 提示使用者檢查檔案權限
   - 檔案過大: 使用串流處理避免記憶體問題

2. **解析層級錯誤**
   - JSON 格式錯誤: 記錄錯誤並跳過該行
   - 缺少必要欄位: 跳過該行並繼續處理
   - 正規表示式不匹配: 跳過該行

3. **資料層級錯誤**
   - 無效的數值: 使用預設值或跳過
   - 時間戳記格式錯誤: 跳過時間記錄但保留其他資料

### 錯誤記錄機制

```python
class ErrorHandler:
    def __init__(self, verbose: bool = False)
    def log_error(self, error_type: str, message: str, line_number: int = None)
    def get_error_summary(self) -> Dict[str, int]
```

## Testing Strategy

### 單元測試

1. **JSONLogParser 測試**
   - 有效 JSON 解析
   - 無效 JSON 處理
   - 缺少欄位處理

2. **HTTPInfoExtractor 測試**
   - 標準 HTTP 訊息解析
   - 邊界情況處理
   - 正規表示式匹配測試

3. **DataAggregator 測試**
   - 資料彙總正確性
   - 統計計算準確性
   - 時間範圍追蹤

### 整合測試

1. **端到端測試**
   - 使用範例日誌檔案測試完整流程
   - 驗證輸出格式正確性
   - 效能測試（大檔案處理）

2. **錯誤情境測試**
   - 損壞的日誌檔案
   - 混合格式的日誌
   - 空檔案處理

### 測試資料

建立多種測試案例：
- 標準格式日誌
- 包含錯誤的日誌
- 大型日誌檔案
- 邊界情況日誌

## Performance Considerations

### 記憶體優化

1. **串流處理**: 使用 `yield` 和迭代器避免載入整個檔案
2. **資料結構選擇**: 使用 `defaultdict` 和 `Counter` 提高效率
3. **正規表示式預編譯**: 避免重複編譯正規表示式

### 處理速度優化

1. **批次處理**: 考慮批次處理多行以提高 I/O 效率
2. **快速失敗**: 儘早識別和跳過無效行
3. **記憶體回收**: 適時清理不需要的資料

### 可擴展性設計

1. **模組化架構**: 便於添加新的分析功能
2. **配置化**: 支援不同的日誌格式和輸出選項
3. **插件系統**: 未來可支援自定義分析器

## Implementation Notes

### 關鍵技術決策

1. **使用 `json` 模組**: Python 標準庫，效能良好且穩定
2. **使用 `re` 模組**: 預編譯正規表示式以提高效能
3. **使用 `datetime` 模組**: 處理 ISO 8601 時間戳記
4. **使用 `dataclasses`**: 提供清晰的資料模型定義

### 外部依賴

最小化外部依賴，主要使用 Python 標準庫：
- `json`: JSON 解析
- `re`: 正規表示式
- `datetime`: 時間處理
- `dataclasses`: 資料模型
- `typing`: 型別提示
- `collections`: 高效資料結構

### 程式碼組織

```
analyze_logs.py
├── main()                    # 主程式入口
├── LogFileReader            # 檔案讀取
├── JSONLogParser           # JSON 解析
├── HTTPInfoExtractor       # HTTP 資訊提取
├── DataAggregator          # 資料彙總
├── StatisticsCalculator    # 統計計算
├── ResultFormatter         # 結果格式化
└── ErrorHandler            # 錯誤處理
```
