# Implementation Plan

- [x] 1. 建立專案結構和核心資料模型
  - 建立 `analyze_logs.py` 檔案的基本結構
  - 定義 `HTTPRequestInfo` 和 `EndpointStats` 資料類別
  - 實作基本的型別提示和匯入語句
  - _Requirements: 1.1, 2.1_

- [x] 2. 實作 JSON 日誌解析功能
  - [x] 2.1 建立 JSONLogParser 類別
    - 實作 `parse_log_line()` 方法來解析 JSON 字串
    - 實作 `extract_rendered_message()` 方法提取 RenderedMessage 欄位
    - 實作 `extract_timestamp()` 方法提取和轉換時間戳記
    - 加入 JSON 解析錯誤處理
    - _Requirements: 1.2, 1.3, 5.1_

  - [x] 2.2 建立 JSON 解析的單元測試
    - 撰寫測試有效 JSON 解析的測試案例
    - 撰寫測試無效 JSON 處理的測試案例
    - 撰寫測試缺少欄位處理的測試案例
    - _Requirements: 1.4, 4.2_

- [x] 3. 實作 HTTP 資訊提取功能
  - [x] 3.1 建立 HTTPInfoExtractor 類別
    - 實作正規表示式 `HTTP "(\w+)" "([^"]+)" responded (\d{3}) in ([\d\.]+) ms`
    - 實作 `extract_http_info()` 方法從 RenderedMessage 提取資訊
    - 加入正規表示式不匹配的錯誤處理
    - 預編譯正規表示式以提高效能
    - _Requirements: 1.3, 2.1, 4.3_

  - [x] 3.2 建立 HTTP 資訊提取的單元測試
    - 撰寫測試標準 HTTP 訊息解析的測試案例
    - 撰寫測試邊界情況處理的測試案例
    - 撰寫測試正規表示式匹配的測試案例
    - _Requirements: 1.4_

- [x] 4. 實作檔案讀取和串流處理
  - [x] 4.1 建立 LogFileReader 類別
    - 實作 `read_lines()` 方法使用迭代器逐行讀取檔案
    - 實作 `is_file_accessible()` 方法檢查檔案存取權限
    - 加入檔案不存在和權限錯誤的處理
    - _Requirements: 1.1, 4.1, 6.1_

  - [x] 4.2 建立檔案讀取的單元測試
    - 撰寫測試正常檔案讀取的測試案例
    - 撰寫測試檔案不存在錯誤處理的測試案例
    - 撰寫測試權限錯誤處理的測試案例
    - _Requirements: 4.1, 4.2_

- [x] 5. 實作資料彙總和統計功能
  - [x] 5.1 建立 DataAggregator 類別
    - 實作 `add_request()` 方法累計 endpoint 統計資料
    - 實作以 (RequestMethod, RequestPath) 為鍵的資料結構
    - 實作狀態碼統計和回應時間記錄功能
    - 實作 `get_time_range()` 方法追蹤時間範圍
    - _Requirements: 2.1, 2.2, 2.3, 5.2, 5.3_

  - [x] 5.2 建立 StatisticsCalculator 類別
    - 實作 `calculate_average_elapsed()` 方法計算平均回應時間
    - 實作 `format_status_code_distribution()` 方法格式化狀態碼分佈
    - 確保計算公式正確：總回應時間 / 總請求次數
    - _Requirements: 3.1, 3.2_

  - [x] 5.3 建立資料彙總的單元測試
    - 撰寫測試資料彙總正確性的測試案例
    - 撰寫測試統計計算準確性的測試案例
    - 撰寫測試時間範圍追蹤的測試案例
    - _Requirements: 2.4, 3.4_

- [x] 6. 實作結果格式化和輸出功能
  - [x] 6.1 建立 ResultFormatter 類別
    - 實作 `format_results()` 方法以表格形式格式化結果
    - 實作 `format_time_range()` 方法格式化時間範圍顯示
    - 確保輸出包含 endpoint 識別、總請求數、狀態碼分佈和平均回應時間
    - _Requirements: 3.3, 5.4_

  - [x] 6.2 建立結果格式化的單元測試
    - 撰寫測試表格格式輸出的測試案例
    - 撰寫測試時間範圍格式化的測試案例
    - 撰寫測試完整結果顯示的測試案例
    - _Requirements: 3.4_

- [x] 7. 實作錯誤處理和日誌記錄
  - [x] 7.1 建立 ErrorHandler 類別
    - 實作 `log_error()` 方法記錄各種錯誤類型
    - 實作 `get_error_summary()` 方法提供錯誤統計
    - 實作詳細錯誤訊息和行號追蹤
    - _Requirements: 4.1, 4.2, 4.3, 4.4_

  - [x] 7.2 整合錯誤處理到各個組件
    - 在 JSONLogParser 中整合錯誤處理
    - 在 HTTPInfoExtractor 中整合錯誤處理
    - 在 LogFileReader 中整合錯誤處理
    - _Requirements: 1.4, 4.4_

- [x] 8. 實作主程式邏輯和整合
  - [x] 8.1 建立主程式 main() 函數
    - 整合所有組件形成完整的處理流程
    - 實作命令列參數處理（檔案路徑）
    - 實作整體的錯誤處理和程式退出邏輯
    - _Requirements: 1.1, 4.1, 6.3_

  - [x] 8.2 實作完整的處理流程
    - 串接檔案讀取 → JSON 解析 → HTTP 提取 → 資料彙總 → 結果輸出
    - 確保每個步驟的錯誤都能被適當處理
    - 實作處理進度的基本回饋機制
    - _Requirements: 1.5, 6.2, 6.4_

- [x] 9. 效能優化和記憶體管理
  - [x] 9.1 實作記憶體效率優化
    - 確保使用串流處理而非一次性載入全部內容
    - 優化資料結構選擇（使用 defaultdict 和 Counter）
    - 實作適時的記憶體回收機制
    - _Requirements: 6.1, 6.2, 6.3_

  - [x] 9.2 實作處理速度優化
    - 預編譯所有正規表示式
    - 實作快速失敗機制跳過無效行
    - 優化字串處理和 JSON 解析效能
    - _Requirements: 6.4_

- [x] 10. 建立整合測試和驗證
  - [x] 10.1 建立端到端測試
    - 使用實際的 `jkopay-api-onlinepay.txt` 檔案進行測試
    - 驗證輸出格式的正確性和完整性
    - 測試大檔案處理的效能和穩定性
    - _Requirements: 1.5, 3.4, 6.4_

  - [x] 10.2 建立錯誤情境測試
    - 測試損壞的日誌檔案處理
    - 測試混合格式的日誌處理
    - 測試空檔案和極端情況處理
    - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 11. 文件撰寫和程式碼清理
  - [x] 11.1 加入程式碼文件和註解
    - 為所有類別和方法加入 docstring
    - 加入關鍵演算法的內聯註解
    - 確保程式碼符合 PEP 8 風格指南
    - _Requirements: 所有需求的可維護性_

  - [x] 11.2 建立使用說明和範例
    - 撰寫程式使用方法的說明
    - 提供範例輸出格式展示
    - 建立故障排除指南
    - _Requirements: 使用者體驗相關需求_
