# Product Overview

## JKoPay API Log Analyzer

A specialized Python tool for analyzing JKoPay API online payment log files. The tool parses structured JSON log entries to extract HTTP request information and provides statistical analysis of API endpoint performance.

## Core Functionality

- Parses JSON-formatted log files containing HTTP request/response data
- Extracts HTTP method, path, status code, and response time from log entries
- Aggregates statistics by API endpoint (method + path combination)
- Calculates average response times and status code distributions
- Provides tabular output for performance analysis

## Target Use Case

Designed specifically for analyzing JKoPay's online payment API logs to monitor:
- API endpoint performance metrics
- Response time patterns
- Status code distributions
- Request volume analysis

The tool focuses on the `RenderedMessage` field pattern: `HTTP "METHOD" "PATH" responded STATUS in TIME ms`
