# Technology Stack

## Language & Runtime
- **Python 3.7+** - Primary development language
- Uses modern Python features like dataclasses, type hints, and f-strings

## Core Libraries
- **json** - JSON parsing for log entries
- **re** - Regular expression matching for HTTP message extraction
- **collections** - defaultdict and Counter for efficient data aggregation
- **dataclasses** - Type-safe data structures for HTTPRequestInfo and EndpointStats
- **datetime** - Timestamp parsing and handling
- **typing** - Type annotations for better code clarity
- **unittest** - Built-in testing framework

## Architecture Patterns
- **Object-oriented design** with focused single-responsibility classes
- **Stream processing** - Line-by-line file reading to handle large files efficiently
- **Error handling** - Graceful handling of malformed JSON and regex mismatches
- **Pre-compiled regex** - Performance optimization for pattern matching

## Code Style
- **PEP 8** compliance for Python style guidelines
- **Type hints** throughout the codebase
- **Docstrings** for all classes and methods
- **Chinese comments** for implementation details (matching existing codebase)

## Common Commands

### Running the analyzer
```bash
python analyze_logs.py
```

### Running tests
```bash
python -m unittest test_json_parser.py
python -m unittest discover -s . -p "test_*.py"
```

### Code quality checks
```bash
# Style checking (if flake8 is available)
flake8 analyze_logs.py

# Type checking (if mypy is available)
mypy analyze_logs.py
```

## Performance Considerations
- Uses iterator-based file reading to minimize memory usage
- Pre-compiles regex patterns for better performance
- Implements fast-fail mechanisms for invalid log lines
