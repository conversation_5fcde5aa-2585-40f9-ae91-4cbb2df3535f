# 專案結構

## 根目錄檔案
- `analyze_logs.py` - 主要的日誌分析工具，包含核心邏輯和資料模型
- `test_json_parser.py` - JSONLogParser 類別的單元測試
- `jkopay-api-onlinepay.txt` - JKoPay API 日誌資料檔案（JSON 格式）
- `development_plan.md` - 開發計畫和實作步驟說明

## 核心類別架構

### 資料模型
- `HTTPRequestInfo` - HTTP 請求資訊的資料類別
- `EndpointStats` - API endpoint 統計資料的資料類別

### 處理類別
- `JSONLogParser` - JSON 日誌解析器
  - `parse_log_line()` - 解析 JSON 字串
  - `extract_rendered_message()` - 提取 RenderedMessage 欄位
  - `extract_timestamp()` - 提取時間戳記

### 預計擴展的類別（根據任務規劃）
- `HTTPInfoExtractor` - HTTP 資訊提取器
- `LogFileReader` - 日誌檔案讀取器
- `DataAggregator` - 資料彙總器
- `StatisticsCalculator` - 統計計算器
- `ResultFormatter` - 結果格式化器
- `ErrorHandler` - 錯誤處理器

## 檔案命名慣例
- 主要模組：`analyze_logs.py`
- 測試檔案：`test_*.py` 格式
- 文件檔案：使用 `.md` 格式，中文命名

## 資料流向
```
日誌檔案 → JSONLogParser → HTTPInfoExtractor → DataAggregator → StatisticsCalculator → ResultFormatter → 輸出結果
```

## 測試結構
- 每個主要類別都有對應的測試檔案
- 使用 Python 內建的 `unittest` 框架
- 測試涵蓋正常情況、邊界情況和錯誤處理

## 設定檔案
- `.kiro/` - Kiro IDE 相關設定
- `__pycache__/` - Python 編譯快取（應被忽略）
