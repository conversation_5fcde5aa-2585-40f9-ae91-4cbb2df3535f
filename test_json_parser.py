"""
JSONLogParser 類別的單元測試

測試 JSON 日誌解析功能，包括有效解析、錯誤處理和邊界情況。
"""

import unittest
from datetime import datetime
from analyze_logs import JSONLogParser


class TestJSONLogParser(unittest.TestCase):
    """JSONLogParser 類別的測試案例"""

    def setUp(self):
        """設置測試環境"""
        self.parser = JSONLogParser()

    def test_parse_valid_json_log_line(self):
        """測試有效 JSON 解析"""
        # 測試標準的 JSON 日誌行
        valid_json = '{"Timestamp": "2023-01-01T12:00:00.123456Z", "RenderedMessage": "HTTP \\"GET\\" \\"/api/test\\" responded 200 in 45.67 ms", "Level": "Information"}'

        result = self.parser.parse_log_line(valid_json)

        self.assertIsNotNone(result)
        self.assertIsInstance(result, dict)
        self.assertEqual(result["Timestamp"], "2023-01-01T12:00:00.123456Z")
        self.assertEqual(result["Level"], "Information")
        self.assertIn("RenderedMessage", result)

    def test_parse_empty_line(self):
        """測試空行處理"""
        result = self.parser.parse_log_line("")
        self.assertIsNone(result)

        result = self.parser.parse_log_line("   ")
        self.assertIsNone(result)

        result = self.parser.parse_log_line("\n")
        self.assertIsNone(result)

    def test_parse_invalid_json(self):
        """測試無效 JSON 處理"""
        # 測試格式錯誤的 JSON
        invalid_jsons = [
            '{"Timestamp": "2023-01-01T12:00:00Z", "RenderedMessage": "test"',  # 缺少結尾括號
            '{"Timestamp": "2023-01-01T12:00:00Z" "RenderedMessage": "test"}',  # 缺少逗號
            '{Timestamp: "2023-01-01T12:00:00Z", "RenderedMessage": "test"}',   # 鍵沒有引號
            'not json at all',  # 完全不是 JSON
            '{"Timestamp": }',  # 值缺失
        ]

        for invalid_json in invalid_jsons:
            with self.subTest(json_string=invalid_json):
                result = self.parser.parse_log_line(invalid_json)
                self.assertIsNone(result)

    def test_extract_rendered_message_valid(self):
        """測試有效的 RenderedMessage 提取"""
        # 測試包含 RenderedMessage 的資料
        log_data = {
            "Timestamp": "2023-01-01T12:00:00Z",
            "RenderedMessage": "HTTP \"GET\" \"/api/test\" responded 200 in 45.67 ms",
            "Level": "Information"
        }

        result = self.parser.extract_rendered_message(log_data)

        self.assertIsNotNone(result)
        self.assertEqual(result, "HTTP \"GET\" \"/api/test\" responded 200 in 45.67 ms")

    def test_extract_rendered_message_missing_field(self):
        """測試缺少 RenderedMessage 欄位的處理"""
        # 測試沒有 RenderedMessage 欄位的資料
        log_data_without_field = {
            "Timestamp": "2023-01-01T12:00:00Z",
            "Level": "Information"
        }

        result = self.parser.extract_rendered_message(log_data_without_field)
        self.assertIsNone(result)

        # 測試 RenderedMessage 為 None 的情況
        log_data_none_field = {
            "Timestamp": "2023-01-01T12:00:00Z",
            "RenderedMessage": None,
            "Level": "Information"
        }

        result = self.parser.extract_rendered_message(log_data_none_field)
        self.assertIsNone(result)

        # 測試 RenderedMessage 不是字串的情況
        log_data_wrong_type = {
            "Timestamp": "2023-01-01T12:00:00Z",
            "RenderedMessage": 12345,
            "Level": "Information"
        }

        result = self.parser.extract_rendered_message(log_data_wrong_type)
        self.assertIsNone(result)

    def test_extract_rendered_message_invalid_input(self):
        """測試無效輸入的處理"""
        # 測試非字典輸入
        invalid_inputs = [None, "string", 123, [], set()]

        for invalid_input in invalid_inputs:
            with self.subTest(input_data=invalid_input):
                result = self.parser.extract_rendered_message(invalid_input)
                self.assertIsNone(result)

    def test_extract_timestamp_valid_formats(self):
        """測試有效時間戳記格式的提取"""
        # 測試各種有效的 ISO 8601 格式
        test_cases = [
            {
                "input": {"Timestamp": "2023-01-01T12:00:00.123456Z"},
                "expected_year": 2023,
                "expected_month": 1,
                "expected_day": 1,
                "expected_hour": 12
            },
            {
                "input": {"Timestamp": "2023-12-31T23:59:59Z"},
                "expected_year": 2023,
                "expected_month": 12,
                "expected_day": 31,
                "expected_hour": 23
            },
            {
                "input": {"Timestamp": "2023-06-15T14:30:45.123Z"},
                "expected_year": 2023,
                "expected_month": 6,
                "expected_day": 15,
                "expected_hour": 14
            }
        ]

        for test_case in test_cases:
            with self.subTest(timestamp=test_case["input"]["Timestamp"]):
                result = self.parser.extract_timestamp(test_case["input"])

                self.assertIsNotNone(result)
                self.assertIsInstance(result, datetime)
                self.assertEqual(result.year, test_case["expected_year"])
                self.assertEqual(result.month, test_case["expected_month"])
                self.assertEqual(result.day, test_case["expected_day"])
                self.assertEqual(result.hour, test_case["expected_hour"])

    def test_extract_timestamp_missing_field(self):
        """測試缺少 Timestamp 欄位的處理"""
        # 測試沒有 Timestamp 欄位
        log_data_without_timestamp = {
            "RenderedMessage": "HTTP \"GET\" \"/api/test\" responded 200 in 45.67 ms",
            "Level": "Information"
        }

        result = self.parser.extract_timestamp(log_data_without_timestamp)
        self.assertIsNone(result)

        # 測試 Timestamp 為 None
        log_data_none_timestamp = {
            "Timestamp": None,
            "RenderedMessage": "HTTP \"GET\" \"/api/test\" responded 200 in 45.67 ms"
        }

        result = self.parser.extract_timestamp(log_data_none_timestamp)
        self.assertIsNone(result)

        # 測試 Timestamp 不是字串
        log_data_wrong_type = {
            "Timestamp": 1234567890,
            "RenderedMessage": "HTTP \"GET\" \"/api/test\" responded 200 in 45.67 ms"
        }

        result = self.parser.extract_timestamp(log_data_wrong_type)
        self.assertIsNone(result)

    def test_extract_timestamp_invalid_formats(self):
        """測試無效時間戳記格式的處理"""
        invalid_timestamps = [
            {"Timestamp": "not a timestamp"},
            {"Timestamp": "2023-13-01T12:00:00Z"},  # 無效月份
            {"Timestamp": "2023-01-32T12:00:00Z"},  # 無效日期
            {"Timestamp": "2023-01-01T25:00:00Z"},  # 無效小時
            {"Timestamp": "2023/01/01 12:00:00"},   # 錯誤格式
            {"Timestamp": ""},                       # 空字串
        ]

        for invalid_data in invalid_timestamps:
            with self.subTest(timestamp=invalid_data["Timestamp"]):
                result = self.parser.extract_timestamp(invalid_data)
                self.assertIsNone(result)

    def test_extract_timestamp_invalid_input(self):
        """測試無效輸入的處理"""
        # 測試非字典輸入
        invalid_inputs = [None, "string", 123, [], set()]

        for invalid_input in invalid_inputs:
            with self.subTest(input_data=invalid_input):
                result = self.parser.extract_timestamp(invalid_input)
                self.assertIsNone(result)

    def test_integration_parse_and_extract(self):
        """測試整合場景：解析 JSON 並提取欄位"""
        # 測試完整的處理流程
        json_line = '{"Timestamp": "2023-01-01T12:00:00.123Z", "RenderedMessage": "HTTP \\"POST\\" \\"/api/payment\\" responded 201 in 123.45 ms", "Level": "Information"}'

        # 解析 JSON
        parsed_data = self.parser.parse_log_line(json_line)
        self.assertIsNotNone(parsed_data)

        # 提取 RenderedMessage
        rendered_message = self.parser.extract_rendered_message(parsed_data)
        self.assertIsNotNone(rendered_message)
        self.assertEqual(rendered_message, "HTTP \"POST\" \"/api/payment\" responded 201 in 123.45 ms")

        # 提取時間戳記
        timestamp = self.parser.extract_timestamp(parsed_data)
        self.assertIsNotNone(timestamp)
        self.assertEqual(timestamp.year, 2023)
        self.assertEqual(timestamp.month, 1)
        self.assertEqual(timestamp.day, 1)


if __name__ == '__main__':
    unittest.main()
