# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 專案概述

這是一個 JKoPay API 日誌分析工具，專門用於解析和分析 JSON 格式的 API 日誌檔案。該工具使用 Python 標準庫，不需要額外的依賴套件。

## 常用命令

### 執行主程式
```bash
# 處理單一檔案
python analyze_logs.py <日誌檔案路徑>

# 處理多個檔案
python analyze_logs.py <檔案1> <檔案2> <檔案3>
python analyze_logs.py *.txt

# 配合時間範圍過濾
python analyze_logs.py file1.txt file2.txt --start-time "2025-08-07 10:00:00" --end-time "2025-08-07 11:00:00"
```

### 執行測試
```bash
# 執行所有測試
python -m unittest discover -s . -p "test_*.py"

# 執行特定測試檔案
python -m unittest test_json_parser.py -v
python -m unittest test_http_extractor.py -v
python -m unittest test_log_file_reader.py -v
python -m unittest test_data_aggregator.py -v
python -m unittest test_result_formatter.py -v
python -m unittest test_error_handler.py -v
python -m unittest test_integration.py -v
python -m unittest test_error_scenarios.py -v
```

### 開發和調試
```bash
# 檢查程式語法
python -m py_compile analyze_logs.py

# 測試檔案存取性（用於調試）
python -c "from analyze_logs import LogFileReader, ErrorHandler; reader = LogFileReader('test.txt', ErrorHandler()); print(reader.is_file_accessible())"

# 測試時間解析功能
python -c "from analyze_logs import parse_datetime; print(parse_datetime('2025-08-07 10:00:00'))"

# 快速測試小範例（前10行）
head -10 jkopay-api-onlinepay.txt | python -c "
import sys, json
from analyze_logs import JSONLogParser, HTTPInfoExtractor, ErrorHandler
parser = JSONLogParser(ErrorHandler())
extractor = HTTPInfoExtractor(ErrorHandler())
for line in sys.stdin:
    data = parser.parse_log_line(line.strip())
    if data:
        msg = parser.extract_rendered_message(data)
        if msg:
            info = extractor.extract_http_info(msg)
            if info: print(f'{info.method} {info.path} -> {info.status_code}')
"
```

## 程式架構

### 核心架構模式
該專案採用模組化設計，主要由以下核心類別組成：

1. **資料模型層**
   - `HTTPRequestInfo`: HTTP 請求資訊的資料類別
   - `EndpointStats`: API endpoint 統計資料的資料類別

2. **解析層**
   - `JSONLogParser`: JSON 日誌解析器
   - `HTTPInfoExtractor`: HTTP 資訊提取器

3. **處理層**
   - `LogFileReader`: 日誌檔案讀取器（串流處理）
   - `DataAggregator`: 資料彙總器
   - `StatisticsCalculator`: 統計計算器

4. **輸出層**
   - `ResultFormatter`: 結果格式化器
   - `ErrorHandler`: 錯誤處理器

### 資料流架構

**單檔案處理流程**：
```
日誌檔案 → LogFileReader → JSONLogParser → HTTPInfoExtractor → DataAggregator → StatisticsCalculator → ResultFormatter
                                                                           ↓
                                                                    ErrorHandler（錯誤處理）
```

**多檔案處理流程**：
```
main() → process_single_file() (per file) → 共享的 DataAggregator → ResultFormatter
   ↓            ↓
檔案清單     錯誤隔離（單檔失敗不影響其他檔案）
```

**多檔案處理的關鍵組件**：
- `process_single_file()`: 處理單一檔案的函數，提供錯誤隔離
- 共享的 `DataAggregator`: 將所有檔案的資料彙總到同一個實例中
- 檔案狀態追蹤: 記錄每個檔案的處理結果（成功/失敗）

### 關鍵設計模式
- **串流處理**: `LogFileReader` 使用逐行讀取避免記憶體溢出
- **預編譯正規表達式**: 使用 `HTTP_PATTERN` 提高解析效能
- **錯誤隔離**: `ErrorHandler` 集中處理各種錯誤情況，多檔案處理時單一檔案錯誤不影響其他檔案
- **統計彙總**: 使用 `defaultdict` 和 `Counter` 優化資料結構
- **時間範圍過濾**: `TimeRangeFilter` 類別提供靈活的時間範圍過濾功能
- **模組化函數設計**: `process_single_file()` 函數封裝單檔案處理邏輯，便於測試和重用

### 測試架構
- 每個核心類別都有對應的單元測試
- `test_integration.py`: 整合測試
- `test_error_scenarios.py`: 錯誤情境測試
- 所有測試使用 Python 標準庫的 `unittest` 框架

### 日誌格式支援
工具專門處理包含以下 RenderedMessage 格式的 JSON 日誌：
```
HTTP "METHOD" "PATH" responded STATUS in TIME ms
```

### 重要注意事項
- 該工具使用 Python 3.7+ 標準庫，無外部依賴
- 採用串流處理模式，適合處理大型日誌檔案
- 支援多檔案處理，可同時分析多個日誌檔案
- 支援時間範圍過濾，可指定開始和結束時間
- 錯誤率 50-70% 屬正常範圍（因日誌包含非 HTTP 訊息）
- 多檔案處理時，部分檔案失敗不會影響其他檔案的處理
- 所有字串使用繁體中文