# JKoPay API 日誌分析工具

這是一個專門用於分析 JKoPay API 線上支付日誌檔案的 Python 工具。工具會解析 JSON 格式的日誌檔案，提取 HTTP 請求資訊，並提供詳細的統計分析結果。

## 功能特色

- **JSON 日誌解析**: 解析 JSON 格式的日誌檔案並提取相關欄位
- **HTTP 資訊提取**: 從 RenderedMessage 中提取 HTTP 方法、路徑、狀態碼和回應時間
- **統計分析**: 計算每個 API endpoint 的請求數、狀態碼分佈和平均回應時間
- **多檔案處理**: 支援同時處理多個日誌檔案，並將結果統一彙總分析
- **時間範圍過濾**: 支援指定開始和結束時間，只分析特定時間範圍內的日誌
- **時間範圍追蹤**: 顯示日誌檔案涵蓋的時間範圍和持續時間
- **容錯處理**: 多檔案處理時，單一檔案失敗不會影響其他檔案的處理
- **錯誤處理**: 優雅地處理各種錯誤情況並提供詳細的錯誤報告
- **記憶體效率**: 使用串流處理，可處理大型日誌檔案而不會耗盡記憶體
- **高效能**: 預編譯正規表示式和優化的資料結構，提供快速的處理速度

## 系統需求

- Python 3.7 或更高版本
- 無需額外的第三方套件（僅使用 Python 標準庫）

## 安裝

1. 下載 `analyze_logs.py` 檔案到您的工作目錄
2. 確保您有 Python 3.7+ 環境

## 使用方法

### 基本用法

```bash
# 分析單一檔案
python analyze_logs.py <日誌檔案路徑>

# 分析多個檔案
python analyze_logs.py <檔案1> <檔案2> <檔案3>

# 使用萬用字元分析多個檔案
python analyze_logs.py *.txt

# 指定時間範圍分析
python analyze_logs.py <檔案> --start-time "2025-08-07 10:00:00" --end-time "2025-08-07 11:00:00"
```

### 基本範例

```bash
# 分析單一檔案
python analyze_logs.py jkopay-api-onlinepay.txt

# 分析多個檔案
python analyze_logs.py log1.txt log2.txt log3.txt

# 分析目錄下所有 txt 檔案
python analyze_logs.py *.txt
```

### 時間範圍過濾範例

```bash
# 只分析上午10點到11點的日誌
python analyze_logs.py jkopay-api-onlinepay.txt --start-time "2025-08-07 10:00:00" --end-time "2025-08-07 11:00:00"

# 分析從特定時間開始的所有日誌
python analyze_logs.py *.txt --start-time "2025-08-07 10:30:00"

# 分析到特定時間結束的所有日誌
python analyze_logs.py *.txt --end-time "2025-08-07 12:00:00"
```

### 支援的時間格式

工具支援多種時間格式：
- `"2025-08-07 10:00:00"` (完整日期時間)
- `"2025-08-07 10:00"` (省略秒數)
- `"2025-08-07"` (只有日期)
- `"2025-08-07T10:00:00"` (ISO 格式)
- `"2025-08-07T10:00:00.123456"` (含微秒)
- `"2025-08-07T10:00:00+08:00"` (含時區)

## 輸出格式

### 單檔案分析輸出範例

```
開始分析 1 個日誌檔案:
  1. jkopay-api-onlinepay.txt
正在處理...

處理檔案 1/1: jkopay-api-onlinepay.txt
檔案 jkopay-api-onlinepay.txt: 處理 4256 行，找到 1618 個有效請求
累計進度: 4256 行，1618 個有效請求

============================================================
JKoPay API 日誌分析結果
==================================================

分析時間範圍: 2025-08-07 10:49:40 至 2025-08-07 10:58:39 (持續時間: 8 分 58 秒)

API Endpoint 統計資料:
----------------------------------------------------------------------------------------------------
方法       路徑                             請求數      狀態碼分佈                     平均回應時間(ms)     
----------------------------------------------------------------------------------------------------
GET      /platform/inquiry              802      200: 802                  13.08          
POST     /platform/entry                245      200: 245                  21.47          
POST     /consumerApp/v2/confirm        216      200: 216                  849.40         
GET      /web/paymentRouter             218      302: 218                  2.08           
GET      /external/service-r/knock      71       200: 71                   9.95           
...
----------------------------------------------------------------------------------------------------

總計: 13 個 API endpoints，1618 個請求
```

### 多檔案分析輸出範例

```
開始分析 3 個日誌檔案:
  1. log1.txt
  2. log2.txt
  3. log3.txt
正在處理...

處理檔案 1/3: log1.txt
檔案 log1.txt: 處理 1000 行，找到 500 個有效請求
累計進度: 1000 行，500 個有效請求

處理檔案 2/3: log2.txt
檔案 log2.txt: 處理 2000 行，找到 800 個有效請求
累計進度: 3000 行，1300 個有效請求

處理檔案 3/3: log3.txt
檔案 log3.txt 處理失敗: 無法存取檔案 'log3.txt'

處理摘要:
  總檔案數: 3
  成功處理: 2
  處理失敗: 1
  失敗檔案:
    log3.txt: 無法存取檔案 'log3.txt'

所有檔案處理完成！總共處理 3000 行，找到 1300 個有效請求。

============================================================
JKoPay API 日誌分析結果
==================================================

處理檔案數: 3

分析時間範圍: 2025-08-07 09:00:00 至 2025-08-07 12:00:00 (持續時間: 3 小時)

API Endpoint 統計資料:
... (統計來自所有成功處理的檔案)
```

### 時間範圍過濾輸出範例

```
時間範圍過濾: 從 2025-08-07 10:00:00 至 2025-08-07 11:00:00
開始分析 1 個日誌檔案:
  1. jkopay-api-onlinepay.txt

... (處理過程)

時間範圍過濾結果:
  總處理請求數: 1618
  過濾排除數: 800
  包含在範圍內: 818
  包含率: 50.6%

============================================================
JKoPay API 日誌分析結果
==================================================
... (只顯示時間範圍內的統計結果)
```

## 支援的日誌格式

工具專門設計來處理包含以下格式 RenderedMessage 的 JSON 日誌：

```
HTTP "METHOD" "PATH" responded STATUS in TIME ms
```

範例：
```json
{
  "Timestamp": "2025-08-07T10:49:40.3401541+08:00",
  "Level": "Information",
  "RenderedMessage": "HTTP \"GET\" \"/platform/inquiry\" responded 200 in 8.4695 ms"
}
```

## 錯誤處理

工具具有強大的錯誤處理能力：

- **JSON 解析錯誤**: 跳過無效的 JSON 行並繼續處理
- **格式不匹配**: 跳過不符合 HTTP 訊息格式的行
- **檔案存取錯誤**: 提供清楚的錯誤訊息，多檔案處理時單一檔案失敗不影響其他檔案
- **容錯機制**: 部分檔案無法處理時，會繼續處理其他檔案並在最終結果中報告
- **時間解析錯誤**: 自動嘗試多種時間格式，無法解析時提供詳細錯誤訊息
- **記憶體管理**: 使用串流處理避免記憶體溢出

## 效能特性

- **串流處理**: 逐行讀取檔案，記憶體使用量不隨檔案大小增長
- **預編譯正規表示式**: 提高模式匹配效能
- **高效資料結構**: 使用 defaultdict 和 Counter 優化資料彙總
- **處理速度**: 在一般硬體上可達到 30,000+ 行/秒的處理速度

## 故障排除

### 常見問題

#### 1. 檔案不存在錯誤
```
錯誤: 無法存取檔案 'filename.txt'
```
**解決方案**: 檢查檔案路徑是否正確，確保檔案存在且可讀取。

#### 2. 沒有找到有效資料
```
沒有找到有效的 HTTP 請求資料。
```
**可能原因**:
- 日誌檔案格式不正確
- 檔案中沒有符合預期格式的 HTTP 訊息
- 所有日誌行都有格式錯誤

**解決方案**: 檢查日誌檔案是否包含正確格式的 RenderedMessage 欄位。

#### 3. 高錯誤率
如果看到高錯誤率（如 60%+），這通常是正常的，因為：
- 日誌檔案可能包含非 HTTP 請求的其他類型日誌
- 某些日誌行可能不符合預期的格式

#### 4. 部分檔案處理失敗
```
處理摘要:
  總檔案數: 3
  成功處理: 2
  處理失敗: 1
  失敗檔案:
    nonexistent.txt: 無法存取檔案 'nonexistent.txt'
```
**解決方案**: 檢查失敗檔案的路徑和權限，修正後重新執行。已成功處理的檔案結果仍會正常顯示。

#### 5. 時間範圍過濾沒有結果
```
時間範圍過濾結果:
  總處理請求數: 1618
  過濾排除數: 1618
  包含在範圍內: 0
  包含率: 0.0%
```
**解決方案**: 檢查指定的時間範圍是否正確，確保日誌檔案包含該時間範圍內的資料。

#### 6. 記憶體不足
如果處理極大檔案時遇到記憶體問題：
- 工具已經使用串流處理，但如果仍有問題，可以考慮分割檔案
- 檢查是否有其他程式佔用大量記憶體
- 使用時間範圍過濾來處理較小的資料集

### 除錯模式

如需更詳細的錯誤資訊，可以修改程式碼中的 `ErrorHandler` 初始化：

```python
error_handler = ErrorHandler(verbose=True)
```

這會在處理過程中即時顯示錯誤訊息。

## 開發和測試

### 執行測試

工具包含完整的測試套件：

```bash
# 執行所有測試
python -m unittest discover -s . -p "test_*.py"

# 執行特定測試
python -m unittest test_json_parser.py -v
python -m unittest test_http_extractor.py -v
python -m unittest test_log_file_reader.py -v
python -m unittest test_data_aggregator.py -v
python -m unittest test_result_formatter.py -v
python -m unittest test_error_handler.py -v
python -m unittest test_integration.py -v
python -m unittest test_error_scenarios.py -v
```

### 程式碼結構

```
analyze_logs.py          # 主程式檔案
├── HTTPRequestInfo      # HTTP 請求資訊資料類別
├── EndpointStats        # API endpoint 統計資料類別
├── JSONLogParser        # JSON 日誌解析器
├── HTTPInfoExtractor    # HTTP 資訊提取器
├── LogFileReader        # 日誌檔案讀取器
├── DataAggregator       # 資料彙總器
├── StatisticsCalculator # 統計計算器
├── ResultFormatter      # 結果格式化器
├── ErrorHandler         # 錯誤處理器
└── main()               # 主程式入口點
```

## 授權

此工具為內部使用而開發，請遵循公司的軟體使用政策。

## 版本歷史

- **v1.1.0**: 多檔案和時間範圍支援版本
  - 新增多檔案處理功能，支援同時分析多個日誌檔案
  - 新增時間範圍過濾功能，支援指定開始和結束時間
  - 改進錯誤處理，單一檔案失敗不影響其他檔案處理
  - 增強結果顯示，包含檔案處理狀態和時間過濾統計
  - 新增 `process_single_file()` 函數提供更好的模組化設計

- **v1.0.0**: 初始版本
  - 基本的 JSON 日誌解析功能
  - HTTP 資訊提取和統計分析
  - 完整的錯誤處理和測試套件

## 聯絡資訊

如有問題或建議，請聯絡開發團隊。
