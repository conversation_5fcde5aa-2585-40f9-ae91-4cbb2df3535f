"""
ResultFormatter 類別的單元測試

測試表格格式輸出、時間範圍格式化和完整結果顯示功能。
"""

import unittest
from datetime import datetime, timedelta
from analyze_logs import ResultFormatter, EndpointStats


class TestResultFormatter(unittest.TestCase):
    """測試 ResultFormatter 類別"""

    def setUp(self):
        """設定測試環境"""
        self.formatter = ResultFormatter()

    def test_format_results_with_data(self):
        """測試有資料時的結果格式化"""
        # 建立測試資料
        aggregated_data = {
            ("GET", "/api/users"): EndpointStats(
                count=10,
                status_codes={200: 8, 404: 2},
                elapsed_times=[100.0, 150.0, 200.0, 120.0, 180.0, 90.0, 110.0, 160.0, 140.0, 130.0]
            ),
            ("POST", "/api/orders"): EndpointStats(
                count=5,
                status_codes={201: 4, 400: 1},
                elapsed_times=[250.0, 300.0, 280.0, 320.0, 290.0]
            )
        }

        time_range = (
            datetime(2023, 1, 1, 10, 0, 0),
            datetime(2023, 1, 1, 12, 0, 0)
        )

        # 格式化結果
        result = self.formatter.format_results(aggregated_data, time_range)

        # 驗證結果包含預期內容
        self.assertIn("JKoPay API 日誌分析結果", result)
        self.assertIn("分析時間範圍:", result)
        self.assertIn("API Endpoint 統計資料:", result)
        self.assertIn("GET", result)
        self.assertIn("POST", result)
        self.assertIn("/api/users", result)
        self.assertIn("/api/orders", result)
        self.assertIn("200: 8, 404: 2", result)
        self.assertIn("201: 4, 400: 1", result)
        self.assertIn("總計: 2 個 API endpoints，15 個請求", result)

    def test_format_results_empty_data(self):
        """測試空資料的結果格式化"""
        aggregated_data = {}
        time_range = (None, None)

        result = self.formatter.format_results(aggregated_data, time_range)
        self.assertEqual(result, "沒有找到有效的日誌資料。")

    def test_format_results_single_endpoint(self):
        """測試單一 endpoint 的結果格式化"""
        aggregated_data = {
            ("GET", "/api/test"): EndpointStats(
                count=3,
                status_codes={200: 3},
                elapsed_times=[100.0, 200.0, 300.0]
            )
        }

        time_range = (None, None)

        result = self.formatter.format_results(aggregated_data, time_range)

        self.assertIn("GET", result)
        self.assertIn("/api/test", result)
        self.assertIn("200: 3", result)
        self.assertIn("200.00", result)  # 平均回應時間
        self.assertIn("總計: 1 個 API endpoints，3 個請求", result)

    def test_format_results_long_path_truncation(self):
        """測試長路徑的截斷處理"""
        long_path = "/api/very/long/path/that/exceeds/thirty/characters/limit"
        aggregated_data = {
            ("GET", long_path): EndpointStats(
                count=1,
                status_codes={200: 1},
                elapsed_times=[100.0]
            )
        }

        time_range = (None, None)

        result = self.formatter.format_results(aggregated_data, time_range)

        # 檢查路徑是否被截斷
        self.assertIn("...", result)
        # 確保截斷後的路徑不超過 30 個字符
        lines = result.split('\n')
        for line in lines:
            if "GET" in line and "..." in line:
                # 找到包含 GET 和 ... 的行，檢查格式
                parts = line.split()
                if len(parts) >= 2:
                    path_part = parts[1]
                    self.assertLessEqual(len(path_part), 30)

    def test_format_time_range_both_times(self):
        """測試有開始和結束時間的格式化"""
        start_time = datetime(2023, 1, 1, 10, 0, 0)
        end_time = datetime(2023, 1, 1, 12, 30, 45)

        result = self.formatter.format_time_range(start_time, end_time)

        self.assertIn("2023-01-01 10:00:00", result)
        self.assertIn("2023-01-01 12:30:45", result)
        self.assertIn("持續時間:", result)
        self.assertIn("2 小時 30 分", result)

    def test_format_time_range_only_start(self):
        """測試只有開始時間的格式化"""
        start_time = datetime(2023, 1, 1, 10, 0, 0)
        end_time = None

        result = self.formatter.format_time_range(start_time, end_time)
        self.assertIn("開始於 2023-01-01 10:00:00", result)

    def test_format_time_range_only_end(self):
        """測試只有結束時間的格式化"""
        start_time = None
        end_time = datetime(2023, 1, 1, 12, 0, 0)

        result = self.formatter.format_time_range(start_time, end_time)
        self.assertIn("結束於 2023-01-01 12:00:00", result)

    def test_format_time_range_both_none(self):
        """測試兩個時間都為 None 的格式化"""
        result = self.formatter.format_time_range(None, None)
        self.assertEqual(result, "無法確定時間範圍")

    def test_format_duration_seconds(self):
        """測試秒級持續時間格式化"""
        duration = timedelta(seconds=45)
        result = self.formatter._format_duration(duration)
        self.assertEqual(result, "45 秒")

    def test_format_duration_minutes(self):
        """測試分鐘級持續時間格式化"""
        duration = timedelta(minutes=5, seconds=30)
        result = self.formatter._format_duration(duration)
        self.assertEqual(result, "5 分 30 秒")

    def test_format_duration_hours(self):
        """測試小時級持續時間格式化"""
        duration = timedelta(hours=2, minutes=15)
        result = self.formatter._format_duration(duration)
        self.assertEqual(result, "2 小時 15 分")

    def test_format_duration_days(self):
        """測試天級持續時間格式化"""
        duration = timedelta(days=1, hours=3)
        result = self.formatter._format_duration(duration)
        self.assertEqual(result, "1 天 3 小時")

    def test_format_duration_zero(self):
        """測試零持續時間格式化"""
        duration = timedelta(seconds=0)
        result = self.formatter._format_duration(duration)
        self.assertEqual(result, "0 秒")

    def test_format_results_sorted_endpoints(self):
        """測試 endpoint 排序"""
        # 建立無序的測試資料
        aggregated_data = {
            ("POST", "/api/users"): EndpointStats(count=1, status_codes={201: 1}, elapsed_times=[100.0]),
            ("GET", "/api/orders"): EndpointStats(count=1, status_codes={200: 1}, elapsed_times=[150.0]),
            ("GET", "/api/users"): EndpointStats(count=1, status_codes={200: 1}, elapsed_times=[120.0]),
            ("DELETE", "/api/users"): EndpointStats(count=1, status_codes={204: 1}, elapsed_times=[80.0]),
        }

        time_range = (None, None)
        result = self.formatter.format_results(aggregated_data, time_range)

        # 檢查排序順序（先按方法，再按路徑）
        lines = result.split('\n')
        data_lines = []
        for line in lines:
            if line.strip() and not line.startswith('-') and not line.startswith('=') and \
               ('GET' in line or 'POST' in line or 'DELETE' in line):
                if '方法' not in line:  # 跳過標題行
                    data_lines.append(line.strip())

        # 驗證排序順序
        self.assertTrue(len(data_lines) >= 4)
        # DELETE 應該在前面，然後是 GET（按路徑排序），最後是 POST
        self.assertIn("DELETE", data_lines[0])
        self.assertIn("GET", data_lines[1])
        self.assertIn("/api/orders", data_lines[1])
        self.assertIn("GET", data_lines[2])
        self.assertIn("/api/users", data_lines[2])
        self.assertIn("POST", data_lines[3])

    def test_format_results_average_calculation(self):
        """測試平均回應時間計算"""
        aggregated_data = {
            ("GET", "/api/test"): EndpointStats(
                count=3,
                status_codes={200: 3},
                elapsed_times=[100.0, 200.0, 300.0]  # 平均應該是 200.0
            )
        }

        time_range = (None, None)
        result = self.formatter.format_results(aggregated_data, time_range)

        # 檢查平均回應時間是否正確顯示
        self.assertIn("200.00", result)


if __name__ == '__main__':
    unittest.main()
