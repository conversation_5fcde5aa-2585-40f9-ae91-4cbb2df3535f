"""
錯誤情境測試

測試損壞的日誌檔案處理、混合格式的日誌處理和空檔案等極端情況。
"""

import unittest
import tempfile
import os
import sys
import io
from contextlib import redirect_stdout, redirect_stderr
from analyze_logs import (
    LogFileReader, JSONLogParser, HTTPInfoExtractor,
    DataAggregator, ResultFormatter, ErrorHandler
)


class TestErrorScenarios(unittest.TestCase):
    """錯誤情境測試類別"""

    def setUp(self):
        """設定測試環境"""
        self.error_handler = ErrorHandler(verbose=False)

    def test_corrupted_json_log_file(self):
        """測試損壞的 JSON 日誌檔案處理"""
        # 建立包含損壞 JSON 的測試檔案
        corrupted_content = '''{"Timestamp":"2025-08-07T10:49:40.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/platform/inquiry\\" responded 200 in 8.4695 ms"}
{invalid json line}
{"Timestamp":"2025-08-07T10:49:41.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"POST\\" \\"/platform/entry\\" responded 201 in 15.2 ms"}
{"incomplete": "json"
{"Timestamp":"2025-08-07T10:49:42.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/platform/status\\" responded 200 in 5.1 ms"}'''

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file.write(corrupted_content)
            temp_file_path = temp_file.name

        try:
            # 建立組件
            file_reader = LogFileReader(temp_file_path, self.error_handler)
            json_parser = JSONLogParser(self.error_handler)
            http_extractor = HTTPInfoExtractor(self.error_handler)
            data_aggregator = DataAggregator()

            # 處理檔案
            valid_requests = 0
            for line in file_reader.read_lines():
                self.error_handler.increment_processed_lines()
                log_data = json_parser.parse_log_line(line)
                if log_data:
                    rendered_message = json_parser.extract_rendered_message(log_data)
                    if rendered_message:
                        http_info = http_extractor.extract_http_info(rendered_message)
                        if http_info:
                            data_aggregator.add_request(http_info)
                            valid_requests += 1

            # 驗證結果
            self.assertEqual(valid_requests, 3, "應該找到 3 個有效請求")
            self.assertTrue(self.error_handler.has_errors(), "應該有錯誤記錄")

            error_summary = self.error_handler.get_error_summary()
            self.assertIn("json_parse_error", error_summary, "應該有 JSON 解析錯誤")
            self.assertGreater(error_summary["json_parse_error"], 0, "應該有多個 JSON 解析錯誤")

        finally:
            os.unlink(temp_file_path)

    def test_mixed_format_log_file(self):
        """測試混合格式的日誌處理"""
        # 建立包含混合格式的測試檔案
        mixed_content = '''{"Timestamp":"2025-08-07T10:49:40.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/users\\" responded 200 in 10.5 ms"}
Plain text log line without JSON format
[2025-08-07 10:49:41] INFO: Some other log format
{"Timestamp":"2025-08-07T10:49:42.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"POST\\" \\"/api/orders\\" responded 201 in 25.3 ms"}
ERROR: Something went wrong
{"Timestamp":"2025-08-07T10:49:43.3401541+08:00","Level":"Information","RenderedMessage":"[ENTRY CREATED] Not an HTTP message"}
{"Timestamp":"2025-08-07T10:49:44.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"DELETE\\" \\"/api/users/123\\" responded 204 in 8.1 ms"}
<xml><log>XML format log</log></xml>'''

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file.write(mixed_content)
            temp_file_path = temp_file.name

        try:
            # 建立組件
            file_reader = LogFileReader(temp_file_path, self.error_handler)
            json_parser = JSONLogParser(self.error_handler)
            http_extractor = HTTPInfoExtractor(self.error_handler)
            data_aggregator = DataAggregator()

            # 處理檔案
            valid_requests = 0
            line_number = 0
            for line in file_reader.read_lines():
                line_number += 1
                self.error_handler.increment_processed_lines()
                log_data = json_parser.parse_log_line(line, line_number)
                if log_data:
                    rendered_message = json_parser.extract_rendered_message(log_data)
                    if rendered_message:
                        http_info = http_extractor.extract_http_info(rendered_message)
                        if http_info:
                            data_aggregator.add_request(http_info)
                            valid_requests += 1

            # 驗證結果
            self.assertEqual(valid_requests, 3, "應該找到 3 個有效的 HTTP 請求")
            self.assertTrue(self.error_handler.has_errors(), "應該有錯誤記錄")

            error_summary = self.error_handler.get_error_summary()
            # 應該有 JSON 解析錯誤和正規表示式不匹配錯誤
            self.assertIn("json_parse_error", error_summary)
            self.assertIn("regex_mismatch", error_summary)

            # 驗證彙總資料
            aggregated_data = data_aggregator.get_aggregated_data()
            self.assertEqual(len(aggregated_data), 3, "應該有 3 個不同的 endpoint")

        finally:
            os.unlink(temp_file_path)

    def test_empty_log_file(self):
        """測試空檔案處理"""
        # 建立空檔案
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file_path = temp_file.name

        try:
            # 建立組件
            file_reader = LogFileReader(temp_file_path, self.error_handler)
            json_parser = JSONLogParser(self.error_handler)
            http_extractor = HTTPInfoExtractor(self.error_handler)
            data_aggregator = DataAggregator()
            result_formatter = ResultFormatter()

            # 處理檔案
            line_count = 0
            for line in file_reader.read_lines():
                line_count += 1

            # 驗證結果
            self.assertEqual(line_count, 0, "空檔案應該沒有行")

            # 測試格式化空結果
            aggregated_data = data_aggregator.get_aggregated_data()
            time_range = data_aggregator.get_time_range()
            result = result_formatter.format_results(aggregated_data, time_range)

            self.assertEqual(result, "沒有找到有效的日誌資料。")

        finally:
            os.unlink(temp_file_path)

    def test_file_with_only_empty_lines(self):
        """測試只包含空行的檔案"""
        # 建立只包含空行的檔案
        empty_lines_content = '\n\n\n   \n\t\n\n'

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file.write(empty_lines_content)
            temp_file_path = temp_file.name

        try:
            # 建立組件
            file_reader = LogFileReader(temp_file_path, self.error_handler)
            json_parser = JSONLogParser(self.error_handler)
            data_aggregator = DataAggregator()

            # 處理檔案
            line_count = 0
            for line in file_reader.read_lines():
                line_count += 1
                self.error_handler.increment_processed_lines()
                json_parser.parse_log_line(line, line_count)

            # 驗證結果
            self.assertGreater(line_count, 0, "應該讀取到一些行")
            self.assertTrue(self.error_handler.has_errors(), "應該有空行錯誤")

            error_summary = self.error_handler.get_error_summary()
            self.assertIn("empty_line", error_summary)

        finally:
            os.unlink(temp_file_path)

    def test_nonexistent_file(self):
        """測試不存在的檔案"""
        nonexistent_file = "this_file_does_not_exist.txt"

        # 建立組件
        file_reader = LogFileReader(nonexistent_file, self.error_handler)

        # 檢查檔案存取性
        accessible = file_reader.is_file_accessible()
        self.assertFalse(accessible, "不存在的檔案應該不可存取")
        self.assertTrue(self.error_handler.has_errors(), "應該有錯誤記錄")

        # 嘗試讀取應該拋出異常
        with self.assertRaises(FileNotFoundError):
            list(file_reader.read_lines())

    def test_permission_denied_file(self):
        """測試權限被拒絕的檔案（在支援的系統上）"""
        # 建立測試檔案
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file.write("test content")
            temp_file_path = temp_file.name

        try:
            # 嘗試移除讀取權限（在 Unix 系統上）
            if os.name != 'nt':  # 不是 Windows
                os.chmod(temp_file_path, 0o000)

                file_reader = LogFileReader(temp_file_path, self.error_handler)

                # 檢查檔案存取性
                accessible = file_reader.is_file_accessible()
                self.assertFalse(accessible, "沒有權限的檔案應該不可存取")

                # 嘗試讀取應該拋出異常
                with self.assertRaises(PermissionError):
                    list(file_reader.read_lines())
            else:
                # Windows 系統跳過此測試
                self.skipTest("權限測試在 Windows 上跳過")

        finally:
            # 恢復權限並刪除檔案
            if os.name != 'nt':
                os.chmod(temp_file_path, 0o644)
            os.unlink(temp_file_path)

    def test_extremely_large_json_line(self):
        """測試極大的 JSON 行"""
        # 建立包含極大 JSON 行的檔案
        large_data = "x" * 10000  # 10KB 的資料
        large_json_content = f'{{"Timestamp":"2025-08-07T10:49:40.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/large\\" responded 200 in 100.5 ms","LargeData":"{large_data}"}}'

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file.write(large_json_content)
            temp_file_path = temp_file.name

        try:
            # 建立組件
            file_reader = LogFileReader(temp_file_path, self.error_handler)
            json_parser = JSONLogParser(self.error_handler)
            http_extractor = HTTPInfoExtractor(self.error_handler)
            data_aggregator = DataAggregator()

            # 處理檔案
            valid_requests = 0
            for line in file_reader.read_lines():
                self.error_handler.increment_processed_lines()
                log_data = json_parser.parse_log_line(line)
                if log_data:
                    rendered_message = json_parser.extract_rendered_message(log_data)
                    if rendered_message:
                        http_info = http_extractor.extract_http_info(rendered_message)
                        if http_info:
                            data_aggregator.add_request(http_info)
                            valid_requests += 1

            # 驗證結果
            self.assertEqual(valid_requests, 1, "應該成功處理大型 JSON 行")

        finally:
            os.unlink(temp_file_path)

    def test_malformed_http_messages(self):
        """測試格式錯誤的 HTTP 訊息"""
        # 建立包含各種格式錯誤 HTTP 訊息的檔案
        malformed_content = '''{"Timestamp":"2025-08-07T10:49:40.3401541+08:00","Level":"Information","RenderedMessage":"HTTP GET /api/users responded 200 in 10.5 ms"}
{"Timestamp":"2025-08-07T10:49:41.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"POST\\" responded 201 in 15.2 ms"}
{"Timestamp":"2025-08-07T10:49:42.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/orders\\" responded in 20.1 ms"}
{"Timestamp":"2025-08-07T10:49:43.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"DELETE\\" \\"/api/users/123\\" responded 204"}
{"Timestamp":"2025-08-07T10:49:44.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"PUT\\" \\"/api/users/456\\" responded 200 in abc ms"}
{"Timestamp":"2025-08-07T10:49:45.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/valid\\" responded 200 in 5.5 ms"}'''

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file.write(malformed_content)
            temp_file_path = temp_file.name

        try:
            # 建立組件
            file_reader = LogFileReader(temp_file_path, self.error_handler)
            json_parser = JSONLogParser(self.error_handler)
            http_extractor = HTTPInfoExtractor(self.error_handler)
            data_aggregator = DataAggregator()

            # 處理檔案
            valid_requests = 0
            line_number = 0
            for line in file_reader.read_lines():
                line_number += 1
                self.error_handler.increment_processed_lines()
                log_data = json_parser.parse_log_line(line, line_number)
                if log_data:
                    rendered_message = json_parser.extract_rendered_message(log_data)
                    if rendered_message:
                        http_info = http_extractor.extract_http_info(rendered_message)
                        if http_info:
                            data_aggregator.add_request(http_info)
                            valid_requests += 1

            # 驗證結果
            self.assertEqual(valid_requests, 1, "只有最後一行是有效的 HTTP 訊息")
            self.assertTrue(self.error_handler.has_errors(), "應該有正規表示式不匹配錯誤")

            error_summary = self.error_handler.get_error_summary()
            self.assertIn("regex_mismatch", error_summary)
            self.assertGreater(error_summary["regex_mismatch"], 0)

        finally:
            os.unlink(temp_file_path)

    def test_invalid_timestamp_formats(self):
        """測試無效的時間戳記格式"""
        # 建立包含無效時間戳記的檔案
        invalid_timestamp_content = '''{"Timestamp":"invalid-timestamp","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/test1\\" responded 200 in 10.5 ms"}
{"Timestamp":"2025-13-45T25:70:80","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/test2\\" responded 200 in 15.2 ms"}
{"Timestamp":"2025-08-07T10:49:42.3401541+08:00","Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/test3\\" responded 200 in 20.1 ms"}
{"Level":"Information","RenderedMessage":"HTTP \\"GET\\" \\"/api/test4\\" responded 200 in 25.3 ms"}'''

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file.write(invalid_timestamp_content)
            temp_file_path = temp_file.name

        try:
            # 建立組件
            file_reader = LogFileReader(temp_file_path, self.error_handler)
            json_parser = JSONLogParser(self.error_handler)
            http_extractor = HTTPInfoExtractor(self.error_handler)
            data_aggregator = DataAggregator()

            # 處理檔案
            valid_requests = 0
            for line in file_reader.read_lines():
                self.error_handler.increment_processed_lines()
                log_data = json_parser.parse_log_line(line)
                if log_data:
                    rendered_message = json_parser.extract_rendered_message(log_data)
                    if rendered_message:
                        http_info = http_extractor.extract_http_info(rendered_message)
                        if http_info:
                            timestamp = json_parser.extract_timestamp(log_data)
                            data_aggregator.add_request(http_info, timestamp)
                            valid_requests += 1

            # 驗證結果
            self.assertEqual(valid_requests, 4, "應該處理所有有效的 HTTP 請求")

            # 檢查時間範圍（只有一個有效時間戳記）
            time_range = data_aggregator.get_time_range()
            self.assertIsNotNone(time_range[0], "應該有開始時間")
            self.assertIsNotNone(time_range[1], "應該有結束時間")
            self.assertEqual(time_range[0], time_range[1], "開始和結束時間應該相同（只有一個有效時間戳記）")

        finally:
            os.unlink(temp_file_path)


class TestMainFunctionErrorScenarios(unittest.TestCase):
    """測試主函數的錯誤情境"""

    def test_main_with_nonexistent_file(self):
        """測試主函數處理不存在的檔案"""
        original_argv = sys.argv
        sys.argv = ['analyze_logs.py', 'nonexistent_file.txt']

        try:
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                from analyze_logs import main
                try:
                    main()
                except SystemExit as e:
                    self.assertEqual(e.code, 1, "應該以錯誤碼退出")

            output = stdout_capture.getvalue()
            self.assertIn("錯誤: 無法存取檔案", output)

        finally:
            sys.argv = original_argv

    def test_main_with_empty_file(self):
        """測試主函數處理空檔案"""
        # 建立空檔案
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
            temp_file_path = temp_file.name

        original_argv = sys.argv
        sys.argv = ['analyze_logs.py', temp_file_path]

        try:
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                from analyze_logs import main
                try:
                    main()
                except SystemExit as e:
                    self.assertEqual(e.code, 0, "空檔案應該正常退出")

            output = stdout_capture.getvalue()
            self.assertIn("沒有找到有效的 HTTP 請求資料", output)

        finally:
            sys.argv = original_argv
            os.unlink(temp_file_path)


if __name__ == '__main__':
    unittest.main()
