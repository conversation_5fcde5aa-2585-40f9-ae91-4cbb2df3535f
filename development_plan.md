### 開發計畫與步驟

我將使用 **Python** 來完成這個任務，因為它非常適合處理文字檔案和資料分析，並且語法簡潔易懂。

**步驟一：建立 Python 腳本**
我會建立一個名為 `analyze_logs.py` 的 Python 腳本檔案。

**步驟二：定義核心解析邏輯**
1.  **讀取檔案**：腳本會逐行讀取 `jkopay-api-onlinepay.txt` 檔案，這樣可以有效處理大檔案，避免一次性載入所有內容到記憶體中。
2.  **正規表示式 (Regex) 匹配**：我會使用正規表示式來精準地從每一行日誌中提取所需資訊。正規表示式樣式如下：
    ```regex
    HTTP (\w+) (\S+) responded (\d{3}) in ([\d\.]+) ms
    ```
    - `(\w+)`: 匹配並捕獲 HTTP 方法 (RequestMethod)。
    - `(\S+)`: 匹配並捕獲不包含空格的請求路徑 (RequestPath)。
    - `(\d{3})`: 匹配並捕獲三位數的狀態碼 (StatusCode)。
    - `([\d\.]+)`: 匹配並捕獲包含數字和小數點的回應時間 (Elapsed)。
3.  **錯誤處理**：對於不符合上述格式的日誌行，腳本會直接跳過，確保程式的穩定性。

**步驟三：資料彙總與統計**
1.  **資料結構**：我會使用一個字典 (Dictionary) 來儲存分析結果。
    -   **鍵 (Key)**：由 `(RequestMethod, RequestPath)` 組成的元組 (Tuple)，以唯一識別一個 endpoint。
    -   **值 (Value)**：另一個字典，包含以下資訊：
        -   `count`: 總請求次數。
        -   `status_codes`: 一個字典，用於統計各種狀態碼出現的次數。
        -   `elapsed_times`: 一個列表，儲存該 endpoint 的所有回應時間，用於後續計算平均值。

2.  **資料處理**：
    -   每當成功解析一行日誌，腳本會更新對應 endpoint 的統計資料。
    -   如果 endpoint 是第一次出現，則在字典中為其建立新條目。

**步驟四：計算與結果呈現**
1.  **計算平均回應時間**：在所有日誌行處理完畢後，腳本會遍歷彙總後的資料，計算每個 endpoint 的平均回應時間 (`總回應時間 / 總請求次數`)。
2.  **格式化輸出**：最後，程式會將結果以清晰的表格形式印在終端機上，包含 `Endpoint (Method + Path)`、`總請求數`、`狀態碼分佈` 和 `平均回應時間 (ms)`。
