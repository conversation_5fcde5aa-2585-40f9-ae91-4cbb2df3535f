"""
整合測試

使用實際的日誌檔案進行端到端測試，驗證輸出格式的正確性和完整性。
"""

import unittest
import sys
import io
from contextlib import redirect_stdout, redirect_stderr
from analyze_logs import (
    LogFileReader, JSONLogParser, HTTPInfoExtractor,
    DataAggregator, ResultFormatter, ErrorHandler
)


class TestIntegration(unittest.TestCase):
    """整合測試類別"""

    def setUp(self):
        """設定測試環境"""
        self.log_file_path = "jkopay-api-onlinepay.txt"
        self.error_handler = <PERSON>rrorHandler(verbose=False)

    def test_end_to_end_processing(self):
        """測試端到端處理流程"""
        # 建立各個組件
        file_reader = LogFileReader(self.log_file_path, self.error_handler)
        json_parser = JSONLogParser(self.error_handler)
        http_extractor = HTTPInfoExtractor(self.error_handler)
        data_aggregator = DataAggregator()
        result_formatter = ResultFormatter()

        # 檢查檔案是否可存取
        self.assertTrue(file_reader.is_file_accessible(), "測試檔案應該可以存取")

        # 處理日誌檔案
        line_number = 0
        valid_requests = 0

        for line in file_reader.read_lines():
            line_number += 1
            self.error_handler.increment_processed_lines()

            # 解析 JSON
            log_data = json_parser.parse_log_line(line, line_number)
            if log_data is None:
                continue

            # 提取 RenderedMessage
            rendered_message = json_parser.extract_rendered_message(log_data)
            if rendered_message is None:
                continue

            # 提取 HTTP 資訊
            http_info = http_extractor.extract_http_info(rendered_message)
            if http_info is None:
                continue

            # 提取時間戳記
            timestamp = json_parser.extract_timestamp(log_data)

            # 彙總資料
            data_aggregator.add_request(http_info, timestamp)
            valid_requests += 1

            # 限制測試處理的行數以加快測試速度
            if line_number >= 1000:
                break

        # 驗證處理結果
        self.assertGreater(line_number, 0, "應該處理了一些日誌行")
        self.assertGreater(valid_requests, 0, "應該找到一些有效請求")

        # 取得彙總資料
        aggregated_data = data_aggregator.get_aggregated_data()
        time_range = data_aggregator.get_time_range()

        # 驗證彙總資料
        self.assertGreater(len(aggregated_data), 0, "應該有彙總資料")

        # 格式化結果
        result = result_formatter.format_results(aggregated_data, time_range)

        # 驗證結果格式
        self.assertIn("JKoPay API 日誌分析結果", result)
        self.assertIn("API Endpoint 統計資料:", result)
        self.assertIn("總計:", result)

        # 驗證至少包含一些常見的 endpoint
        found_endpoints = []
        for (method, path), stats in aggregated_data.items():
            found_endpoints.append(f"{method} {path}")
            # 驗證統計資料的完整性
            self.assertGreater(stats.count, 0)
            self.assertGreater(len(stats.status_codes), 0)
            self.assertGreater(len(stats.elapsed_times), 0)

        print(f"找到的 endpoints: {found_endpoints}")

    def test_error_handling_during_processing(self):
        """測試處理過程中的錯誤處理"""
        # 建立組件
        file_reader = LogFileReader(self.log_file_path, self.error_handler)
        json_parser = JSONLogParser(self.error_handler)
        http_extractor = HTTPInfoExtractor(self.error_handler)

        # 處理一些行並故意引入錯誤
        line_count = 0
        for line in file_reader.read_lines():
            line_count += 1
            self.error_handler.increment_processed_lines()

            # 正常處理
            log_data = json_parser.parse_log_line(line, line_count)
            if log_data:
                rendered_message = json_parser.extract_rendered_message(log_data)
                if rendered_message:
                    http_extractor.extract_http_info(rendered_message)

            # 測試錯誤處理
            json_parser.parse_log_line("invalid json", line_count)
            http_extractor.extract_http_info("invalid http message")

            if line_count >= 100:  # 限制測試行數
                break

        # 驗證錯誤被正確記錄
        self.assertTrue(self.error_handler.has_errors())
        error_summary = self.error_handler.get_error_summary()
        self.assertIn("json_parse_error", error_summary)
        self.assertIn("regex_mismatch", error_summary)

    def test_large_file_performance(self):
        """測試大檔案處理效能（簡化版）"""
        import time

        start_time = time.time()

        # 建立組件
        file_reader = LogFileReader(self.log_file_path, self.error_handler)
        json_parser = JSONLogParser(self.error_handler)
        http_extractor = HTTPInfoExtractor(self.error_handler)
        data_aggregator = DataAggregator()

        # 處理檔案
        line_count = 0
        valid_requests = 0

        for line in file_reader.read_lines():
            line_count += 1
            self.error_handler.increment_processed_lines()

            log_data = json_parser.parse_log_line(line, line_count)
            if log_data:
                rendered_message = json_parser.extract_rendered_message(log_data)
                if rendered_message:
                    http_info = http_extractor.extract_http_info(rendered_message)
                    if http_info:
                        timestamp = json_parser.extract_timestamp(log_data)
                        data_aggregator.add_request(http_info, timestamp)
                        valid_requests += 1

        end_time = time.time()
        processing_time = end_time - start_time

        print(f"處理了 {line_count} 行，找到 {valid_requests} 個有效請求")
        print(f"處理時間: {processing_time:.2f} 秒")
        print(f"處理速度: {line_count / processing_time:.0f} 行/秒")

        # 驗證效能（這些是合理的期望值）
        self.assertLess(processing_time, 30, "處理時間應該在 30 秒內")
        self.assertGreater(line_count / processing_time, 100, "處理速度應該至少 100 行/秒")

    def test_output_format_validation(self):
        """測試輸出格式驗證"""
        # 建立測試資料
        file_reader = LogFileReader(self.log_file_path, self.error_handler)
        json_parser = JSONLogParser(self.error_handler)
        http_extractor = HTTPInfoExtractor(self.error_handler)
        data_aggregator = DataAggregator()
        result_formatter = ResultFormatter()

        # 處理少量資料
        line_count = 0
        for line in file_reader.read_lines():
            line_count += 1
            log_data = json_parser.parse_log_line(line, line_count)
            if log_data:
                rendered_message = json_parser.extract_rendered_message(log_data)
                if rendered_message:
                    http_info = http_extractor.extract_http_info(rendered_message)
                    if http_info:
                        timestamp = json_parser.extract_timestamp(log_data)
                        data_aggregator.add_request(http_info, timestamp)

            if line_count >= 500:  # 處理足夠的資料以獲得有意義的結果
                break

        # 格式化結果
        aggregated_data = data_aggregator.get_aggregated_data()
        time_range = data_aggregator.get_time_range()
        result = result_formatter.format_results(aggregated_data, time_range)

        # 驗證輸出格式的各個部分
        lines = result.split('\n')

        # 檢查標題
        title_found = False
        for line in lines:
            if "JKoPay API 日誌分析結果" in line:
                title_found = True
                break
        self.assertTrue(title_found, "應該包含標題")

        # 檢查時間範圍
        time_range_found = False
        for line in lines:
            if "分析時間範圍:" in line:
                time_range_found = True
                break
        self.assertTrue(time_range_found, "應該包含時間範圍")

        # 檢查表格標題
        table_header_found = False
        for line in lines:
            if "方法" in line and "路徑" in line and "請求數" in line:
                table_header_found = True
                break
        self.assertTrue(table_header_found, "應該包含表格標題")

        # 檢查總計資訊
        summary_found = False
        for line in lines:
            if "總計:" in line and "個 API endpoints" in line:
                summary_found = True
                break
        self.assertTrue(summary_found, "應該包含總計資訊")

    def test_memory_usage_stability(self):
        """測試記憶體使用穩定性（簡化版）"""
        import gc

        # 強制垃圾回收
        gc.collect()

        # 建立組件
        file_reader = LogFileReader(self.log_file_path, self.error_handler)
        json_parser = JSONLogParser(self.error_handler)
        http_extractor = HTTPInfoExtractor(self.error_handler)
        data_aggregator = DataAggregator()

        # 處理檔案
        line_count = 0
        for line in file_reader.read_lines():
            line_count += 1
            log_data = json_parser.parse_log_line(line, line_count)
            if log_data:
                rendered_message = json_parser.extract_rendered_message(log_data)
                if rendered_message:
                    http_info = http_extractor.extract_http_info(rendered_message)
                    if http_info:
                        timestamp = json_parser.extract_timestamp(log_data)
                        data_aggregator.add_request(http_info, timestamp)

        # 驗證處理完成
        self.assertGreater(line_count, 1000, "應該處理了足夠的行數")

        # 強制垃圾回收
        gc.collect()

        # 如果程式能執行到這裡而沒有記憶體錯誤，就表示記憶體使用是穩定的
        self.assertTrue(True, "記憶體使用穩定")


class TestMainFunction(unittest.TestCase):
    """測試主函數"""

    def test_main_function_with_valid_file(self):
        """測試主函數處理有效檔案"""
        # 模擬命令列參數
        original_argv = sys.argv
        sys.argv = ['analyze_logs.py', 'jkopay-api-onlinepay.txt']

        try:
            # 捕獲輸出
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                # 導入並執行主函數
                from analyze_logs import main
                try:
                    main()
                except SystemExit as e:
                    # 主函數正常結束時會呼叫 sys.exit(0)
                    self.assertEqual(e.code, 0, "主函數應該正常結束")

            # 檢查輸出
            output = stdout_capture.getvalue()
            self.assertIn("開始分析日誌檔案", output)
            self.assertIn("JKoPay API 日誌分析結果", output)
            self.assertIn("分析完成", output)

        finally:
            # 恢復原始命令列參數
            sys.argv = original_argv

    def test_main_function_with_invalid_arguments(self):
        """測試主函數處理無效參數"""
        # 模擬無效的命令列參數
        original_argv = sys.argv
        sys.argv = ['analyze_logs.py']  # 缺少檔案參數

        try:
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                from analyze_logs import main
                try:
                    main()
                except SystemExit as e:
                    # 應該以錯誤碼退出
                    self.assertEqual(e.code, 1, "應該以錯誤碼退出")

            # 檢查輸出
            output = stdout_capture.getvalue()
            self.assertIn("使用方法:", output)

        finally:
            sys.argv = original_argv


if __name__ == '__main__':
    unittest.main()
